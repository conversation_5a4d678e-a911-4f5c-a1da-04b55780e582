<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP资产管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/jquery-3.7.0.min.js') }}"></script>
    <style>
        /* 全局样式 */
        body {
            background-color: #f5f6fa;
            font-size: 14px;
        }
        
        /* 导航菜单样式 */
        .nav-menu {
            background: #fff;
            padding: 12px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            margin-bottom: 12px;
        }
        
        /* 搜索区域样式 */
        .search-panel {
            background: #fff;
            padding: 12px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            margin-bottom: 12px;
        }
        
        /* 表格样式 */
        .table {
            background: #fff;
            margin-bottom: 12px;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 500;
            padding: 8px;
            font-size: 13px;
            color: #333;
        }
        
        .table td {
            padding: 6px 8px;
            font-size: 13px;
            color: #333;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 4px 8px;
            font-size: 13px;
        }
        
        .btn-sm {
            padding: 2px 6px;
            font-size: 12px;
        }

        /* 恢复紧凑型样式 */
        .form-control {
            padding: 4px 8px;
            font-size: 13px;
            height: auto;
        }

        label {
            font-size: 13px;
            margin-bottom: 0;
        }

        /* 列选择区域样式 */
        .column-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .form-check-inline {
            margin-right: 8px;
            white-space: nowrap;
        }

        .form-check-input {
            margin-right: 4px;
        }

        /* 分页样式 */
        .pagination {
            margin: 0;
        }

        .page-link {
            padding: 4px 8px;
            font-size: 13px;
        }

        /* 卡片样式 */
        .card {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            margin-bottom: 12px;
        }

        .card-body {
            padding: 12px;
        }

        /* 表格容器样式 */
        .table-responsive {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        /* 调整间距 */
        .mb-2 {
            margin-bottom: 8px !important;
        }

        .mb-3 {
            margin-bottom: 12px !important;
        }

        .py-3 {
            padding-top: 12px !important;
            padding-bottom: 12px !important;
        }

        /* 按钮组样式 */
        .btn-group-sm > .btn {
            padding: 2px 6px;
            font-size: 12px;
        }

        /* 操作按钮样式 */
        .btn-operation {
            margin: 2px;
            font-size: 13px;
            padding: 4px 8px;
        }

        /* 表格悬停效果 */
        .table-hover tbody tr:hover {
            background-color: #f5f5f5;
            color: #333;
        }

        /* 确保链接文字可见 */
        .table td a {
            color: #0d6efd;
        }

        .table td a:hover {
            color: #0a58ca;
        }

        /* 确保按钮文字可见 */
        .btn-danger {
            color: #fff !important;
        }

        .btn-primary {
            color: #fff !important;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 导航菜单 -->
        <div class="nav-menu p-3 mb-4">
            <div class="row g-2">
                <div class="col-auto">
                    <a href="{{ url_for('ip.add_form') }}" class="btn btn-success btn-operation">添加记录</a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.duplicate') }}" class="btn btn-primary btn-operation">重复记录</a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.find_duplicates') }}" class="btn btn-primary btn-operation">查找并删除重复记录</a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.merge_records') }}" class="btn btn-primary btn-operation">合并相同IP范围记录</a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.ip_stats') }}" class="btn btn-info btn-operation">IP分组实际网段范围统计</a>
                </div>


                <div class="col-auto">
                    <a href="{{ url_for('ip.merge_ip_ranges') }}" class="btn btn-primary btn-operation">IP段合并工具</a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.group_by_region') }}" class="btn btn-info btn-operation">按地区按实际网段分组</a>
                </div>

                <div class="col-auto">
                    <a href="{{ url_for('ip.check_groups') }}" class="btn btn-warning btn-operation">分组信息检查与修改</a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.check_region') }}" class="btn btn-warning btn-operation">地区信息检查与修改</a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.route_analysis') }}" class="btn btn-primary">
                        查找相同路由路径
                    </a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.port_scan_page') }}" class="btn btn-primary">
                        批量端口扫描
                    </a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.crawler_config_page') }}" class="btn btn-primary">
                        网络爬虫工具
                    </a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.dns_resolve_page') }}" class="btn btn-primary">
                        DNS解析工具
                    </a>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('ip.ip_sort_page') }}" class="btn btn-primary">
                        IP地址整理排序
                    </a>
                </div>



                <div class="col-auto">
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#importModal">
                        导入CSV
                    </button>
                </div>
            </div>
        </div>

        <!-- CSV导入模态框 -->
        <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="importModalLabel">导入CSV文件</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="importForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="csvFile" class="form-label">选择CSV文件</label>
                                <input type="file" class="form-control" id="csvFile" name="file" accept=".csv">
                            </div>
                            <div class="mb-3">
                                <a href="{{ url_for('ip.download_template') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-download"></i> 下载导入模板
                                </a>
                            </div>
                        </form>
                        <div id="importResult" class="mt-3" style="display: none;">
                            <div class="alert" role="alert"></div>
                            <div id="errorDetails" style="display: none;">
                                <h6>导入错误详情：</h6>
                                <ul class="list-unstyled"></ul>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" id="importButton">导入</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- QQ纯真数据库导出模态框已删除 -->

        <!-- 搜索框 -->
        <div class="search-panel">
            <form method="get" action="{{ url_for('ip.search') }}" id="searchForm">
                <h5>查找IP/全局搜索</h5>
                <div class="mb-3">
                    <label class="form-label">请输入IP地址或搜索关键字</label>
                    <textarea name="keyword" class="form-control" rows="5" placeholder="输入IP地址可精确匹配IP范围&#13;&#10;输入其他关键字将在所有字段中搜索&#13;&#10;可输入多个IP地址(每行一个)进行批量查询">{{ keyword if keyword else '' }}</textarea>
                </div>
                <div class="row g-2 align-items-center">
                    <div class="col-auto">
                        <select name="sel" class="form-select" id="searchMethod">
                            <option value="ip">IP搜索</option>
                            <option value="all">全字段搜索</option>
                        </select>
                    </div>
                    <!-- 数据库选择已删除 -->
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 搜索结果展示区域 -->
        <div class="search-results mt-4">
            <!-- Tab导航 -->
            <!-- 搜索结果标签页已简化 -->
            <h5>搜索结果</h5>

            <!-- 搜索结果内容 -->
            <div class="search-results-content">
                    <!-- 本地数据库结果表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>开始IP</th>
                                    <th>结束IP</th>
                                    <th>资产名称</th>
                                    <th>资产组名</th>
                                    <th>资产类型</th>
                                    <th>使用人</th>
                                    <th>系统</th>
                                    <th>MAC地址</th>
                                    <th>制造商</th>
                                    <th>主机域名</th>
                                    <th>备注</th>
                                    <th>服务与端口</th>
                                    <th>详细地址</th>
                                    <th>ISP</th>
                                    <th>国家</th>
                                    <th>省份</th>
                                    <th>城市</th>
                                    <th>县</th>
                                    <th>经度</th>
                                    <th>纬度</th>
                                    <th>使用状态</th>
                                    <th>首次发现时间</th>
                                    <th>最后上线时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in records %}
                                <tr id="tr{{ record.id }}">
                                    <td>{{ record.id }}</td>
                                    <td>{{ record.ipBegin|int_to_ip }}</td>
                                    <td>{{ record.ipEnd|int_to_ip }}</td>
                                    <td>{{ record.name }}</td>
                                    <td>{{ record.group }}</td>
                                    <td>{{ record.type }}</td>
                                    <td>{{ record.user }}</td>
                                    <td>{{ record.system }}</td>
                                    <td>{{ record.mac|format_mac }}</td>
                                    <td>{{ record.manufacturer }}</td>
                                    <td>{{ record.domainName }}</td>
                                    <td>{{ record.remark }}</td>
                                    <td>{{ record.port }}</td>
                                    <td>{{ record.address }}</td>
                                    <td>{{ record.isp }}</td>
                                    <td>{{ record.country }}</td>
                                    <td>{{ record.province }}</td>
                                    <td>{{ record.city }}</td>
                                    <td>{{ record.county }}</td>
                                    <td>{{ record.longitude|format_coordinate }}</td>
                                    <td>{{ record.latitude|format_coordinate }}</td>
                                    <td>{{ record.isused }}</td>
                                    <td>{{ record.firsttime }}</td>
                                    <td>{{ record.lasttime }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-danger del" data-id="{{ record.id }}">删除</button>
                                            <a href="{{ url_for('ip.edit_form', id=record.id) }}" class="btn btn-primary">修改</a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 在表格前添加显示字段设置 -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">显示字段设置</h6>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="selectAll">全选</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" id="selectNone">全不选</button>
                        <button type="button" class="btn btn-sm btn-primary ms-2" id="saveColumns">保存设置</button>
                    </div>
                </div>
                <div class="column-options">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_id" name="columns" value="ID" checked>
                        <label class="form-check-label" for="col_id">ID</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_ipBegin" name="columns" value="开始IP" checked>
                        <label class="form-check-label" for="col_ipBegin">开始IP</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_ipEnd" name="columns" value="结束IP" checked>
                        <label class="form-check-label" for="col_ipEnd">结束IP</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_name" name="columns" value="资产名称" checked>
                        <label class="form-check-label" for="col_name">资产名称</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_group" name="columns" value="资产组名" checked>
                        <label class="form-check-label" for="col_group">资产组名</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_type" name="columns" value="资产类型">
                        <label class="form-check-label" for="col_type">资产类型</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_user" name="columns" value="使用人">
                        <label class="form-check-label" for="col_user">使用人</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_system" name="columns" value="系统">
                        <label class="form-check-label" for="col_system">系统</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_mac" name="columns" value="MAC地址">
                        <label class="form-check-label" for="col_mac">MAC地址</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_manufacturer" name="columns" value="制造商">
                        <label class="form-check-label" for="col_manufacturer">制造商</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_domainName" name="columns" value="主机域名">
                        <label class="form-check-label" for="col_domainName">主机域名</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_remark" name="columns" value="备注">
                        <label class="form-check-label" for="col_remark">备注</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_port" name="columns" value="服务与端口">
                        <label class="form-check-label" for="col_port">服务与端口</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_address" name="columns" value="详细地址">
                        <label class="form-check-label" for="col_address">详细地址</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_isp" name="columns" value="ISP" checked>
                        <label class="form-check-label" for="col_isp">ISP</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_country" name="columns" value="国家">
                        <label class="form-check-label" for="col_country">国家</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_province" name="columns" value="省份">
                        <label class="form-check-label" for="col_province">省份</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_city" name="columns" value="城市">
                        <label class="form-check-label" for="col_city">城市</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_county" name="columns" value="县">
                        <label class="form-check-label" for="col_county">县</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_longitude" name="columns" value="经度">
                        <label class="form-check-label" for="col_longitude">经度</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_latitude" name="columns" value="纬度">
                        <label class="form-check-label" for="col_latitude">纬度</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_isused" name="columns" value="使用状态">
                        <label class="form-check-label" for="col_isused">使用状态</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_firsttime" name="columns" value="首次发现时间">
                        <label class="form-check-label" for="col_firsttime">首次发现时间</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="col_lasttime" name="columns" value="最后上线时间">
                        <label class="form-check-label" for="col_lasttime">最后上线时间</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div class="text-muted">
                共 {{ recordcount }} 条记录，{{ page }}/{{ pagecount }} 页
            </div>
            <nav>
                <ul class="pagination mb-0">
                    <!-- 首页 -->
                    <li class="page-item {% if page == 1 %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('ip.index', page=1) }}">首页</a>
                    </li>
                    
                    <!-- 上一页 -->
                    {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('ip.index', page=page-1) }}">上一页</a>
                    </li>
                    {% endif %}
                    
                    <!-- 页码 -->
                    {% set start_page = [1, page-2]|max %}
                    {% set end_page = [pagecount, page+2]|min %}
                    
                    {% if start_page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('ip.index', page=1) }}">1</a>
                        </li>
                        {% if start_page > 2 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endif %}
                    
                    {% for p in range(start_page, end_page + 1) %}
                    <li class="page-item {% if p == page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('ip.index', page=p) }}">{{ p }}</a>
                    </li>
                    {% endfor %}
                    
                    {% if end_page < pagecount %}
                        {% if end_page < pagecount - 1 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('ip.index', page=pagecount) }}">{{ pagecount }}</a>
                        </li>
                    {% endif %}
                    
                    <!-- 下一页 -->
                    {% if page < pagecount %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('ip.index', page=page+1) }}">下一页</a>
                    </li>
                    {% endif %}
                    
                    <!-- 末页 -->
                    <li class="page-item {% if page == pagecount %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('ip.index', page=pagecount) }}">末页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script>
        $(function() {
            $(".del").click(function() {
                if (confirm("确认要删除？")) {
                    var id = $(this).data('id');
                    $.post("{{ url_for('ip.delete_record', id=0) }}".replace('0', id), function(response) {
                        if (response.status === 'success') {
                            $("#tr" + id).fadeOut();
                        }
                    });
                }
            });

            // 全选按钮
            $('#selectAll').click(function() {
                $('input[name="columns"]').prop('checked', true);
                updateTable();
            });

            // 全不选按钮
            $('#selectNone').click(function() {
                $('input[name="columns"]').prop('checked', false);
                updateTable();
            });

            // 保存设置按钮
            $('#saveColumns').click(function() {
                // 获取所有选中的列
                var selectedColumns = [];
                $('input[name="columns"]:checked').each(function() {
                    selectedColumns.push($(this).val());
                });
                
                // 保存到localStorage
                localStorage.setItem('selectedColumns', JSON.stringify(selectedColumns));
                alert('设置已保存！');
            });

            // 列选择变化时更新表格
            $('input[name="columns"]').change(function() {
                updateTable();
            });

            // 更新表格显示
            function updateTable() {
                $('input[name="columns"]').each(function() {
                    var colName = $(this).val();
                    var isChecked = $(this).prop('checked');
                    // 根据checkbox状态显示/隐藏对应的列
                    $('th:contains("' + colName + '"), td:nth-child(' + ($(this).parent().index() + 1) + ')').toggle(isChecked);
                });
            }

            // 页面加载时从localStorage恢复设置
            var savedColumns = localStorage.getItem('selectedColumns');
            if (savedColumns) {
                savedColumns = JSON.parse(savedColumns);
                $('input[name="columns"]').each(function() {
                    $(this).prop('checked', savedColumns.includes($(this).val()));
                });
                updateTable();
            }

            // CSV导入功能
            $('#importButton').click(function() {
                var formData = new FormData();
                var fileInput = $('#csvFile')[0];
                
                if (fileInput.files.length === 0) {
                    showImportResult('error', '请选择要导入的文件');
                    return;
                }
                
                formData.append('file', fileInput.files[0]);
                
                // 显示加载状态
                $(this).prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 导入中...');
                
                $.ajax({
                    url: '{{ url_for("ip.import_csv") }}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        showImportResult(response.status, response.message, response.errors);
                        if (response.status === 'success') {
                            // 导入成功后刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        }
                    },
                    error: function() {
                        showImportResult('error', '导入过程中发生错误，请重试');
                    },
                    complete: function() {
                        // 恢复按钮状态
                        $('#importButton').prop('disabled', false).text('导入');
                    }
                });
            });
            
            function showImportResult(status, message, errors) {
                var resultDiv = $('#importResult');
                var alert = resultDiv.find('.alert');
                var errorDetails = $('#errorDetails');
                
                // 设置提示框样式和消息
                alert.removeClass('alert-success alert-danger')
                     .addClass(status === 'success' ? 'alert-success' : 'alert-danger')
                     .text(message);
                
                // 显示错误详情（如果有）
                if (errors && errors.length > 0) {
                    var errorList = errorDetails.find('ul').empty();
                    errors.forEach(function(error) {
                        errorList.append(`<li>第 ${error.row} 行: ${error.error}</li>`);
                    });
                    errorDetails.show();
                } else {
                    errorDetails.hide();
                }
                
                resultDiv.show();
            }

            // 搜索类型相关代码已删除

            // 搜索表单相关代码已简化

            // QQwry 导出相关代码已删除
                            
                            // 更新进度条
                            const percentage = data.percentage || 0;
                            $('#export-progress-bar').css('width', percentage + '%');
                            $('#export-progress-bar').attr('aria-valuenow', percentage);
                            $('#export-progress-bar').text(percentage.toFixed(1) + '%');
                            
                            // 更新进度文本
                            let statusText = '';
                            if (data.status === 'running') {
                                statusText = `已处理: ${data.current}/${data.total}，找到记录: ${data.records_found || 0}`;
                                if (data.processes) {
                                    statusText += `，运行进程: ${data.processes}`;
                                }
                                // 继续轮询
                                setTimeout(checkExportProgress, 2000);
                            } else if (data.status === 'completed') {
                                statusText = data.message || '导出完成';
                                $('#export-progress-bar').removeClass('progress-bar-animated');
                            }
                            $('#export-progress-text').text(statusText);
                        } else if (data.status === 'error') {
                            $('#export-progress-container').removeClass('d-none');
                            $('#export-progress-bar').addClass('bg-danger').removeClass('progress-bar-animated');
                            $('#export-progress-text').text('错误: ' + (data.message || '未知错误'));
                        }
                    })
                    .catch(error => console.error('检查导出进度时出错:', error));
            }
            
            // 页面加载时检查进度
            $(document).ready(function() {
                checkExportProgress();
            });
            
            // 模态框关闭时停止轮询
            $('#qqwryExportModal').on('hidden.bs.modal', function() {
                if (exportInterval) {
                    clearInterval(exportInterval);
                }
            });
        });
    </script>
</body>
</html>