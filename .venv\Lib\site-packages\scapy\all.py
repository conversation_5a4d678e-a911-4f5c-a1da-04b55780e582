# SPDX-License-Identifier: GPL-2.0-only
# This file is part of Scapy
# See https://scapy.net/ for more information
# Copyright (C) <PERSON> <<EMAIL>>

"""
Aggregate top level objects from all Scapy modules.
"""

from scapy.base_classes import *
from scapy.config import *
from scapy.dadict import *
from scapy.data import *
from scapy.error import *
from scapy.themes import *
from scapy.arch import *
from scapy.interfaces import *

from scapy.plist import *
from scapy.fields import *
from scapy.packet import *
from scapy.asn1fields import *
from scapy.asn1packet import *

from scapy.utils import *
from scapy.route import *
from scapy.sendrecv import *
from scapy.sessions import *
from scapy.supersocket import *
from scapy.volatile import *
from scapy.as_resolvers import *

from scapy.automaton import *
from scapy.autorun import *

from scapy.main import *
from scapy.consts import *
from scapy.compat import raw  # noqa: F401

from scapy.layers.all import *

from scapy.asn1.asn1 import *
from scapy.asn1.ber import *
from scapy.asn1.mib import *

from scapy.pipetool import *
from scapy.scapypipes import *

if conf.ipv6_enabled:  # noqa: F405
    from scapy.utils6 import *  # noqa: F401
    from scapy.route6 import *  # noqa: F401

from scapy.ansmachine import *
