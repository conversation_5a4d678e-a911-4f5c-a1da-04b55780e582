<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果 - IP资产管理系统</title>
    <link href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet"> -->
    <link href="{{ url_for('static', filename='css/all.min.css') }}" rel="stylesheet">
    <style>
        /* 全局样式 */
        body {
            background-color: #f5f6fa;
            font-size: 14px;
        }
        
        /* 搜索区域样式 */
        .search-panel {
            background: #fff;
            padding: 12px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            margin-bottom: 12px;
        }
        
        /* 表格样式 */
        .table {
            background: #fff;
            margin-bottom: 12px;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 500;
            padding: 8px;
            font-size: 13px;
        }
        
        .table td {
            position: relative;
            padding: 6px 8px;
            font-size: 13px;
            min-height: 32px;
            vertical-align: middle;
        }
        
        /* 匹配项列样式 */
        td[data-column="match"] {
            max-width: 200px;
            word-wrap: break-word;
            white-space: normal;
            min-width: 100px;
        }

        td[data-column="match"] .badge {
            display: inline-block;
            max-width: 100%;
            word-wrap: break-word;
            white-space: normal;
            text-align: left;
        }
        
        /* 可编辑单元格样式 */
        .editable-cell {
            position: relative;
            cursor: pointer;
            padding: 6px 8px;
            min-height: 32px;
        }

        .editable-cell:hover {
            background-color: #f8f9fa;
        }

        .editable-cell.editing {
            padding: 0;
        }

        .edit-container {
            position: relative;
            width: 100%;
        }

        .edit-input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #007bff;
            border-radius: 3px;
            font-size: 13px;
            box-sizing: border-box;
        }

        .edit-buttons {
            position: absolute;
            right: 0;
            top: 100%;
            display: flex;
            gap: 2px;
            background: white;
            padding: 2px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .edit-btn {
            width: 24px;
            height: 24px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            color: white;
            border-radius: 2px;
            font-family: Arial, sans-serif;
            font-size: 16px;
            font-weight: bold;
        }

        .edit-btn.save-btn {
            background-color: #28a745;
        }

        .edit-btn.cancel-btn {
            background-color: #dc3545;
        }

        /* 列选择区域样式 */
        .column-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .column-options label {
            font-size: 13px;
            white-space: nowrap;
            user-select: none;
            cursor: pointer;
        }

        .column-checkbox {
            margin-right: 4px;
        }

        /* 分页样式 */
        .pagination {
            margin: 0;
        }

        .page-link {
            padding: 4px 8px;
            font-size: 13px;
        }

        /* 按钮样式 */
        .btn {
            padding: 4px 8px;
            font-size: 13px;
        }

        .btn-sm {
            padding: 2px 6px;
            font-size: 12px;
        }

        /* 匹配标签样式 */
        .badge {
            font-size: 12px;
            padding: 3px 6px;
        }

        /* 地址单元格样式 */
        .address-cell {
            max-width: 200px;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            min-width: 150px;
            padding: 8px !important;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">搜索结果</h4>
            <a href="{{ url_for('ip.index') }}" class="btn btn-secondary btn-sm">返回</a>
        </div>

        <!-- 搜索结果标题 -->

        <!-- 原有的查询结果显示代码 -->
        {% if search_type == 'ip' and not_found_ips %}
        <div class="alert alert-warning">
            <strong>以下IP未查到匹配记录：</strong>
            <ul>
                {% for ip in not_found_ips %}
                <li>{{ ip }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <!-- 显示/隐藏列控制 -->
        <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>显示/隐藏列：</div>
                <div>
                    <button class="btn btn-sm btn-light me-1" onclick="selectAllColumns()">全选</button>
                    <button class="btn btn-sm btn-light me-1" onclick="unselectAllColumns()">取消全选</button>
                    <button class="btn btn-sm btn-primary" onclick="saveColumnSettings()">保存设置</button>
                </div>
            </div>
            
            <div class="column-options">
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="match" checked> 匹配项
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="id" checked> ID
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="ip_range" checked> IP范围
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="name" checked> 资产名称
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="group" checked> 资产组名
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="user" checked> 使用人
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="type" checked> 类型
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="system" checked> 系统
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="mac" checked> MAC地址
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="manufacturer" checked> 制造商
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="remark" checked> 备注
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="port" checked> 端口
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="coordinates" checked> 经纬度
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="country" checked> 国家
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="province" checked> 省份
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="city" checked> 城市
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="county" checked> 区县
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="address" checked> 地址
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="isp" checked> ISP
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="status" checked> 使用状态
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="time" checked> 时间信息
                </label>
                <label class="me-2">
                    <input type="checkbox" class="column-checkbox" value="operations" checked> 操作
                </label>
            </div>
        </div>

        {% if search_results %}
        <div class="table-responsive">
            <table class="table table-hover table-striped">
                <thead>
                    <tr>
                        <th data-column="match">匹配项</th>
                        <th data-column="id">ID</th>
                        <th data-column="ip_range">开始IP</th>
                        <th data-column="ip_range">结束IP</th>
                        <th data-column="name">资产名称</th>
                        <th data-column="group">资产组名</th>
                        <th data-column="user">使用人</th>
                        <th data-column="type">类型</th>
                        <th data-column="system">系统</th>
                        <th data-column="mac">MAC地址</th>
                        <th data-column="manufacturer">制造商</th>
                        <th data-column="remark">备注</th>
                        <th data-column="port">端口</th>
                        <th data-column="coordinates">经度</th>
                        <th data-column="coordinates">纬度</th>
                        <th data-column="country">国家</th>
                        <th data-column="province">省份</th>
                        <th data-column="city">城市</th>
                        <th data-column="county">区县</th>
                        <th data-column="address">地址</th>
                        <th data-column="isp">ISP</th>
                        <th data-column="status">使用状态</th>
                        <th data-column="time">首次发现</th>
                        <th data-column="time">最后发现</th>
                        <th data-column="operations">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record, matched_query in search_results %}
                    <tr id="tr{{ record.id }}">
                        <td data-column="match">
                            <span class="badge bg-info">{{ matched_query }}</span>
                        </td>
                        <td data-column="id">{{ record.id }}</td>
                        <td data-column="ip_range" class="editable-cell" data-field="ipBegin" data-id="{{ record.id }}">
                            {{ record.ipBegin|int_to_ip }}
                        </td>
                        <td data-column="ip_range" class="editable-cell" data-field="ipEnd" data-id="{{ record.id }}">
                            {{ record.ipEnd|int_to_ip }}
                        </td>
                        <td data-column="name" class="editable-cell" data-field="name" data-id="{{ record.id }}">
                            {{ record.name or '' }}
                        </td>
                        <td data-column="group" class="editable-cell" data-field="group" data-id="{{ record.id }}">
                            {{ record.group or '' }}
                        </td>
                        <td data-column="user" class="editable-cell" data-field="user" data-id="{{ record.id }}">
                            {{ record.user or '' }}
                        </td>
                        <td data-column="type" class="editable-cell" data-field="type" data-id="{{ record.id }}">
                            {{ record.type or '' }}
                        </td>
                        <td data-column="system" class="editable-cell" data-field="system" data-id="{{ record.id }}">
                            {{ record.system or '' }}
                        </td>
                        <td data-column="mac" class="editable-cell" data-field="mac" data-id="{{ record.id }}">
                            {{ record.mac|format_mac }}
                        </td>
                        <td data-column="manufacturer" class="editable-cell" data-field="manufacturer" data-id="{{ record.id }}">
                            {{ record.manufacturer or '' }}
                        </td>
                        <td data-column="remark" class="editable-cell" data-field="remark" data-id="{{ record.id }}">
                            {{ record.remark or '' }}
                        </td>
                        <td data-column="port" class="editable-cell" data-field="port" data-id="{{ record.id }}">
                            {{ record.port or '' }}
                        </td>
                        <td data-column="coordinates" class="editable-cell" data-field="longitude" data-id="{{ record.id }}">
                            {{ record.longitude|format_coordinate }}
                        </td>
                        <td data-column="coordinates" class="editable-cell" data-field="latitude" data-id="{{ record.id }}">
                            {{ record.latitude|format_coordinate }}
                        </td>
                        <td data-column="country" class="editable-cell" data-field="country" data-id="{{ record.id }}">
                            {{ record.country or '' }}
                        </td>
                        <td data-column="province" class="editable-cell" data-field="province" data-id="{{ record.id }}">
                            {{ record.province or '' }}
                        </td>
                        <td data-column="city" class="editable-cell" data-field="city" data-id="{{ record.id }}">
                            {{ record.city or '' }}
                        </td>
                        <td data-column="county" class="editable-cell" data-field="county" data-id="{{ record.id }}">
                            {{ record.county or '' }}
                        </td>
                        <td data-column="address" class="editable-cell" data-field="address" data-id="{{ record.id }}">
                            {{ record.address or '' }}
                        </td>
                        <td data-column="isp" class="editable-cell" data-field="isp" data-id="{{ record.id }}">
                            {{ record.isp or '' }}
                        </td>
                        <td data-column="status" class="editable-cell" data-field="isused" data-id="{{ record.id }}">
                            {{ record.isused }}
                        </td>
                        <td data-column="time">{{ record.firsttime }}</td>
                        <td data-column="time">{{ record.lasttime }}</td>
                        <td data-column="operations">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-danger del" data-id="{{ record.id }}">删除</button>
                                <a href="{{ url_for('ip.edit_form', id=record.id) }}" class="btn btn-primary">修改</a>
                                <button class="btn btn-info trace-route"
                                        data-ip="{{ record.ipBegin }}"
                                        data-fyip-id="{{ record.id }}">ICMP跟踪</button>
                                <button class="btn btn-warning udp-trace-route"
                                        data-ip="{{ record.ipBegin }}"
                                        data-fyip-id="{{ record.id }}">UDP跟踪</button>
                                <button class="btn btn-success tcp-trace-route"
                                        data-ip="{{ record.ipBegin }}"
                                        data-fyip-id="{{ record.id }}">TCP跟踪</button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            未找到匹配的记录
        </div>
        {% endif %}
    </div>

    <!-- 添加模态框用于显示跟踪结果 -->
    <div class="modal fade" id="traceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Traceroute 结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="traceResults">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">跟踪中...</span>
                            </div>
                            <p>正在跟踪路由，请稍候...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery-3.7.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script>
        $(function() {
            // 删除按钮事件
            $(".del").click(function() {
                if (confirm("确认要删除？")) {
                    var id = $(this).data('id');
                    $.post("{{ url_for('ip.delete_record', id=0) }}".replace('0', id), function(response) {
                        if (response.status === 'success') {
                            $(this).closest('tr').fadeOut();
                        }
                    });
                }
            });
            
            // 纯真数据库删除功能已删除
            
            // 导入功能已删除
        });

        // 将函数定义移到全局作用域
        function selectAllColumns() {
            document.querySelectorAll('.column-checkbox').forEach(cb => cb.checked = true);
            updateColumnVisibility();
        }

        function unselectAllColumns() {
            document.querySelectorAll('.column-checkbox').forEach(cb => cb.checked = false);
            updateColumnVisibility();
        }

        function saveColumnSettings() {
            const selectedColumns = Array.from(document.querySelectorAll('.column-checkbox:checked'))
                .map(cb => cb.value);
            
            // 保存到localStorage
            localStorage.setItem('searchResultVisibleColumns', JSON.stringify(selectedColumns));
            alert('设置已保存！');
        }

        function updateColumnVisibility() {
            const selectedColumns = Array.from(document.querySelectorAll('.column-checkbox:checked'))
                .map(cb => cb.value);
            
            // 更新表头
            document.querySelectorAll('th').forEach(th => {
                const columnName = th.getAttribute('data-column');
                if (columnName) {
                    th.style.display = selectedColumns.includes(columnName) ? 'table-cell' : 'none';
                }
            });
            
            // 更新表格内容
            document.querySelectorAll('td').forEach(td => {
                const columnName = td.getAttribute('data-column');
                if (columnName) {
                    td.style.display = selectedColumns.includes(columnName) ? 'table-cell' : 'none';
                }
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从localStorage加载保存的设置
            const savedColumns = localStorage.getItem('searchResultVisibleColumns');
            if (savedColumns) {
                const selectedColumns = JSON.parse(savedColumns);
                document.querySelectorAll('.column-checkbox').forEach(cb => {
                    cb.checked = selectedColumns.includes(cb.value);
                });
            }
            
            // 初始更新列显示状态
            updateColumnVisibility();
            
            // 监听复选框变化
            document.querySelectorAll('.column-checkbox').forEach(cb => {
                cb.addEventListener('change', updateColumnVisibility);
            });
        });

        // ICMP跟踪按钮事件
        $(function() {
            $(".trace-route").click(function() {
                const ipStart = $(this).data('ip');
                const fyipId = $(this).data('fyip-id');
                const modal = new bootstrap.Modal(document.getElementById('traceModal'));
                modal.show();
                
                $.post("{{ url_for('ip.do_traceroute', ip_int=0, fyip_id=0) }}"
                    .replace('0/0', ipStart + '/' + fyipId))
                    .done(function(response) {
                        if (response.status === 'success') {
                            let html = `
                                <div class="alert alert-info">
                                    <strong>目标IP:</strong> ${response.target_ip}
                                    <br>
                                    <strong>跟踪时间:</strong> ${response.trace_time}
                                    <br>
                                    <strong>跟踪类型:</strong> ICMP
                                </div>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>跳数</th>
                                            <th>IP地址</th>
                                            <th>响应时间(ms)</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;
                            
                            response.results.forEach(function(hop) {
                                html += `<tr>
                                    <td>${hop.hop}</td>
                                    <td>${hop.ip}</td>
                                    <td>${hop.ip === '*' ? '-' : hop.rtt}</td>
                                </tr>`;
                            });
                            
                            html += '</tbody></table>';
                            $('#traceResults').html(html);
                        } else {
                            $('#traceResults').html(`<div class="alert alert-danger">
                                跟踪失败: ${response.message}
                            </div>`);
                        }
                    })
                    .fail(function(jqXHR, textStatus, errorThrown) {
                        $('#traceResults').html(`<div class="alert alert-danger">
                            请求失败: ${textStatus}
                        </div>`);
                    });
            });
        });

        // UDP跟踪按钮事件
        $(function() {
            $(".udp-trace-route").click(function() {
                const ipStart = $(this).data('ip');
                const fyipId = $(this).data('fyip-id');
                const modal = new bootstrap.Modal(document.getElementById('traceModal'));
                modal.show();
                
                $.post("{{ url_for('ip.do_udp_traceroute', ip_int=0, fyip_id=0) }}"
                    .replace('0/0', ipStart + '/' + fyipId))
                    .done(function(response) {
                        if (response.status === 'success') {
                            let html = `
                                <div class="alert alert-info">
                                    <strong>目标IP:</strong> ${response.target_ip}
                                    <br>
                                    <strong>跟踪时间:</strong> ${response.trace_time}
                                    <br>
                                    <strong>跟踪类型:</strong> UDP
                                </div>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>跳数</th>
                                            <th>IP地址</th>
                                            <th>响应时间(ms)</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;
                            
                            response.results.forEach(function(hop) {
                                html += `<tr>
                                    <td>${hop.hop}</td>
                                    <td>${hop.ip}</td>
                                    <td>${hop.ip === '*' ? '-' : hop.rtt}</td>
                                </tr>`;
                            });
                            
                            html += '</tbody></table>';
                            $('#traceResults').html(html);
                        } else {
                            $('#traceResults').html(`<div class="alert alert-danger">
                                跟踪失败: ${response.message}
                            </div>`);
                        }
                    })
                    .fail(function(jqXHR, textStatus, errorThrown) {
                        $('#traceResults').html(`<div class="alert alert-danger">
                            请求失败: ${textStatus}
                        </div>`);
                    });
            });
        });

        // TCP跟踪按钮事件
        $(function() {
            $(".tcp-trace-route").click(function() {
                const ipStart = $(this).data('ip');
                const fyipId = $(this).data('fyip-id');
                const modal = new bootstrap.Modal(document.getElementById('traceModal'));
                modal.show();
                
                $.post("{{ url_for('ip.do_tcp_traceroute', ip_int=0, fyip_id=0) }}"
                    .replace('0/0', ipStart + '/' + fyipId))
                    .done(function(response) {
                        if (response.status === 'success') {
                            let html = `
                                <div class="alert alert-info">
                                    <strong>目标IP:</strong> ${response.target_ip}
                                    <br>
                                    <strong>跟踪时间:</strong> ${response.trace_time}
                                    <br>
                                    <strong>跟踪类型:</strong> TCP
                                </div>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>跳数</th>
                                            <th>IP地址</th>
                                            <th>响应时间(ms)</th>
                                        </tr>
                                    </thead>
                                    <tbody>`;
                            
                            response.results.forEach(function(hop) {
                                html += `<tr>
                                    <td>${hop.hop}</td>
                                    <td>${hop.ip}</td>
                                    <td>${hop.ip === '*' ? '-' : hop.rtt}</td>
                                </tr>`;
                            });
                            
                            html += '</tbody></table>';
                            $('#traceResults').html(html);
                        } else {
                            $('#traceResults').html(`<div class="alert alert-danger">
                                跟踪失败: ${response.message}
                            </div>`);
                        }
                    })
                    .fail(function(jqXHR, textStatus, errorThrown) {
                        $('#traceResults').html(`<div class="alert alert-danger">
                            请求失败: ${textStatus}
                        </div>`);
                    });
            });
        });

        // 绑定单元格点击事件
        $('.editable-cell').on('click', function(e) {
            if (!$(this).hasClass('editing')) {
                const cell = this;
                const id = $(cell).data('id');
                const field = $(cell).data('field');
                startEdit(cell, id, field);
            }
        });

        function startEdit(cell, id, field) {
            if (cell.classList.contains('editing')) {
                return;
            }

            const currentValue = $(cell).text().trim();
            $(cell).addClass('editing');
            
            const container = $('<div>').addClass('edit-container');
            const input = $('<input>')
                .addClass('edit-input')
                .val(currentValue)
                .attr('type', 'text');
                
            const buttonsDiv = $('<div>').addClass('edit-buttons');
            
            const saveButton = $('<button>')
                .addClass('edit-btn save-btn')
                .attr('title', '保存')
                .html('✓')
                .on('click', function(e) {
                    e.stopPropagation();
                    saveEdit(cell, id, field, input.val());
                });
                
            const cancelButton = $('<button>')
                .addClass('edit-btn cancel-btn')
                .attr('title', '取消')
                .html('✕')
                .on('click', function(e) {
                    e.stopPropagation();
                    cancelEdit(cell, currentValue);
                });
                
            buttonsDiv.append(saveButton, cancelButton);
            container.append(input, buttonsDiv);
            
            $(cell).empty().append(container);
            input.focus();
            
            // 添加键盘事件
            input.on('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveEdit(cell, id, field, input.val());
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit(cell, currentValue);
                }
            });

            // 点击其他地方关闭编辑
            $(document).one('click', function handler(e) {
                if (!$(cell).has(e.target).length && !$(e.target).is(cell)) {
                    cancelEdit(cell, currentValue);
                }
            });

            // 阻止冒泡
            input.on('click', function(e) {
                e.stopPropagation();
            });
        }

        function saveEdit(cell, id, field, value) {
            // 使用本地数据库更新路由
            var url = "{{ url_for('ip.update_field') }}";

            $.ajax({
                url: url,
                method: 'POST',
                data: {
                    id: id,
                    field: field,
                    value: value
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $(cell).removeClass('editing').text(value || '');
                    } else {
                        alert('保存失败：' + response.message);
                        cancelEdit(cell, value);
                    }
                },
                error: function() {
                    alert('保存失败');
                    cancelEdit(cell, value);
                }
            });
        }

        function cancelEdit(cell, originalValue) {
            $(cell).removeClass('editing').text(originalValue || '');
        }
    </script>
</body>
</html> 