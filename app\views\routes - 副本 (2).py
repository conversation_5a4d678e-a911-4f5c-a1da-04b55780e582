from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app
from app.models.ip import FYIP
from app import db
from app.utils.helpers import int_to_ip, format_mac, format_coordinate, find_optimal_cidrs, ip_to_int
import socket
import struct
import ipaddress
from scapy.all import IP, ICMP, sr1, UDP, TCP
from datetime import datetime
import time

ip_bp = Blueprint('ip', __name__)

@ip_bp.route('/')
def index():
    page = request.args.get('page', 1, type=int)
    pagesize = 40
    
    pagination = FYIP.query.order_by(FYIP.ipBegin).paginate(
        page=page, per_page=pagesize, error_out=False)
    
    return render_template('index.html',
                         records=pagination.items,
                         page=page,
                         recordcount=FYIP.query.count(),
                         pagecount=pagination.pages)

@ip_bp.route('/add', methods=['GET'])
def add_form():
    return render_template('add.html')

@ip_bp.route('/add', methods=['POST'])
def add_record():
    form_data = request.form.to_dict()  # 保存表单数据
    try:
        # 获取表单数据
        ip_begin = form_data.get('ipBegin')
        ip_end = form_data.get('ipEnd')
        
        # 将IP地址转换为整数
        ip_begin_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_begin.split('.')))
        ip_end_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_end.split('.')))
        
        # 处理MAC地址
        mac_str = form_data.get('mac', '')
        if mac_str:
            mac_hex = mac_str.replace(':', '').replace(' ', '')
            mac_bytes = bytes.fromhex(mac_hex)
        else:
            mac_bytes = None
        
        # 创建新记录（移除不存在的字段）
        if 'domainName' in form_data:
            del form_data['domainName']  # 移除不存在的字段
            
        new_record = FYIP(
            ipBegin=ip_begin_int,
            ipEnd=ip_end_int,
            name=form_data.get('name'),
            group=form_data.get('group'),
            user=form_data.get('user'),
            type=form_data.get('type'),
            system=form_data.get('system'),
            mac=mac_bytes,
            manufacturer=form_data.get('manufacturer'),
            remark=form_data.get('remark'),
            port=form_data.get('port'),
            address=form_data.get('address'),
            isp=form_data.get('isp'),
            country=form_data.get('country'),
            province=form_data.get('province'),
            city=form_data.get('city'),
            county=form_data.get('county'),
            longitude=float(form_data.get('longitude')) if form_data.get('longitude') else None,
            latitude=float(form_data.get('latitude')) if form_data.get('latitude') else None,
            isused=int(form_data.get('isused', 1))
        )
        
        db.session.add(new_record)
        db.session.commit()
        
        return redirect(url_for('ip.index'))
    except Exception as e:
        db.session.rollback()
        # 返回表单页面，同时返回错误信息和表单数据
        return render_template('add.html', error=str(e), form=form_data)

@ip_bp.route('/edit/<int:id>', methods=['GET'])
def edit_form(id):
    record = FYIP.query.get_or_404(id)
    return render_template('edit.html', record=record)

@ip_bp.route('/edit/<int:id>', methods=['POST'])
def edit_record(id):
    record = FYIP.query.get_or_404(id)
    try:
        # 获取表单数据
        ip_begin = request.form.get('ipBegin')
        ip_end = request.form.get('ipEnd')
        
        # 将IP地址转换为整数
        ip_begin_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_begin.split('.')))
        ip_end_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_end.split('.')))
        
        # 处理MAC地址
        mac_str = request.form.get('mac', '')
        if mac_str:
            mac_hex = mac_str.replace(':', '').replace(' ', '')
            record.mac = bytes.fromhex(mac_hex)
        else:
            record.mac = None
        
        # 更新记录
        record.ipBegin = ip_begin_int
        record.ipEnd = ip_end_int
        record.name = request.form.get('name')
        record.group = request.form.get('group')
        record.user = request.form.get('user')
        record.type = request.form.get('type')
        record.system = request.form.get('system')
        record.manufacturer = request.form.get('manufacturer')
        record.remark = request.form.get('remark')
        record.port = request.form.get('port')
        record.address = request.form.get('address')
        record.isp = request.form.get('isp')
        record.country = request.form.get('country')
        record.province = request.form.get('province')
        record.city = request.form.get('city')
        record.county = request.form.get('county')
        
        # 处理经纬度
        longitude = request.form.get('longitude', '')
        latitude = request.form.get('latitude', '')
        record.longitude = float(longitude) if longitude.strip() else None
        record.latitude = float(latitude) if latitude.strip() else None
        
        record.isused = int(request.form.get('isused', 1))
        
        db.session.commit()
        return redirect(url_for('ip.index'))
    except Exception as e:
        db.session.rollback()
        return render_template('edit.html', record=record, error=str(e))

@ip_bp.route('/delete/<int:id>', methods=['POST'])
def delete_record(id):
    record = FYIP.query.get_or_404(id)
    try:
        db.session.delete(record)
        db.session.commit()
        return jsonify({'status': 'success'})
    except:
        db.session.rollback()
        return jsonify({'status': 'error'})

@ip_bp.route('/search')
def search():
    keyword = request.args.get('keyword', '').strip()
    search_type = request.args.get('sel', 'all')
    
    if not keyword:
        return render_template('search_result.html', 
                             search_results=[], 
                             search_type=search_type)
    
    # 构建查询
    query = FYIP.query
    search_results = []
    
    if search_type == 'ip':
        # 处理批量IP搜索
        ip_list = [ip.strip() for ip in keyword.split('\n') if ip.strip()]
        conditions = []
        for ip in ip_list:
            try:
                ip_int = ip_to_int(ip)
                conditions.append(
                    db.and_(FYIP.ipBegin <= ip_int, FYIP.ipEnd >= ip_int)
                )
            except:
                continue
        if conditions:
            query = query.filter(db.or_(*conditions))
            
        # 执行查询
        results = query.all()
        
        # 为每个结果找出匹配的具体IP
        for record in results:
            matching_ips = []
            for ip in ip_list:
                try:
                    ip_int = ip_to_int(ip)
                    if record.ipBegin <= ip_int <= record.ipEnd:
                        matching_ips.append(ip)
                except:
                    continue
            if matching_ips:
                search_results.append((record, f"匹配IP: {', '.join(matching_ips)}"))
    
    else:
        # 在所有字段中搜索
        conditions = []
        
        # 文本字段搜索
        text_fields = [
            FYIP.name, FYIP.group, FYIP.address, FYIP.isp,
            FYIP.user, FYIP.type, FYIP.system,
            FYIP.manufacturer, FYIP.remark,
            FYIP.country, FYIP.province, FYIP.city, FYIP.county
        ]
        
        # 特殊字段搜索
        if keyword.isdigit():
            # 端口搜索
            conditions.append(FYIP.port == int(keyword))
        
        # MAC地址搜索 - 如果输入符合MAC地址格式
        if ':' in keyword or '-' in keyword:
            try:
                mac_parts = keyword.replace('-', ':').split(':')
                if len(mac_parts) <= 6:  # MAC地址最多6段
                    mac_pattern = '%' + keyword.replace('-', ':') + '%'
                    conditions.append(FYIP.mac.like(mac_pattern))
            except:
                pass
        
        # 添加文本字段的搜索条件
        for field in text_fields:
            conditions.append(field.ilike(f'%{keyword}%'))
        
        # 组合所有条件
        query = query.filter(db.or_(*conditions))
        
        # 执行查询
        results = query.all()
        
        # 为每个结果找出匹配的字段
        for record in results:
            matched_fields = []
            fields_to_check = ['name', 'group', 'address', 'isp', 'user', 'type', 
                             'system', 'manufacturer', 'remark', 'port',
                             'country', 'province', 'city', 'county']
            
            for field in fields_to_check:
                if hasattr(record, field):
                    field_value = str(getattr(record, field))
                    if field_value and keyword.lower() in field_value.lower():
                        matched_fields.append(field)
            
            # 检查MAC地址
            if record.mac:
                mac_str = format_mac(record.mac)
                if keyword.lower() in mac_str.lower():
                    matched_fields.append('MAC地址')
            
            if matched_fields:
                search_results.append((record, ', '.join(matched_fields)))
    
    return render_template('search_result.html', 
                         search_results=search_results,
                         search_type=search_type)

@ip_bp.route('/duplicate')
def duplicate():
    """查找重复记录"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    pagesize = 40
    
    # 构建子查询来找出重复的IP范围
    subquery = db.session.query(FYIP.ipBegin, FYIP.ipEnd)\
        .group_by(FYIP.ipBegin, FYIP.ipEnd)\
        .having(db.func.count('*') > 1)\
        .subquery()
    
    # 主查询获取具有重复IP范围的完整记录
    query = FYIP.query\
        .join(subquery, db.and_(
            FYIP.ipBegin == subquery.c.ipBegin,
            FYIP.ipEnd == subquery.c.ipEnd
        ))\
        .order_by(FYIP.ipBegin)
    
    # 获取总记录数
    recordcount = query.count()
    
    # 计算总页数
    pagecount = (recordcount + pagesize - 1) // pagesize
    
    # 获取当前页的记录
    records = query.offset((page - 1) * pagesize).limit(pagesize).all()
    
    return render_template('duplicate.html',
                         records=records,
                         page=page,
                         pagecount=pagecount,
                         recordcount=recordcount)

@ip_bp.route('/find_duplicates')
def find_duplicates():
    """查找并删除重复记录"""
    # 查找完全相同的IP范围记录
    duplicates = db.session.query(
        FYIP.ipBegin,
        FYIP.ipEnd,
        db.func.count('*').label('count')
    ).group_by(
        FYIP.ipBegin,
        FYIP.ipEnd
    ).having(
        db.func.count('*') > 1
    ).all()
    
    return render_template('find_duplicates.html', 
                         duplicates=duplicates,
                         get_duplicate_records=get_duplicate_records)

def get_duplicate_records(ip_begin, ip_end):
    """获取指定IP范围的所有重复记录"""
    return FYIP.query.filter(
        FYIP.ipBegin == ip_begin,
        FYIP.ipEnd == ip_end
    ).all()

@ip_bp.route('/delete_duplicates', methods=['POST'])
def delete_duplicates():
    """删除重复记录，保留最早的一条"""
    try:
        ip_begin = request.form.get('ipBegin', type=int)
        ip_end = request.form.get('ipEnd', type=int)
        
        if not ip_begin or not ip_end:
            return jsonify({'status': 'error', 'message': '参数错误'})
        
        # 查找指定IP范围的所有记录
        records = FYIP.query.filter(
            FYIP.ipBegin == ip_begin,
            FYIP.ipEnd == ip_end
        ).order_by(
            FYIP.id  # 按ID排序，保留最早的记录
        ).all()
        
        if len(records) <= 1:
            return jsonify({'status': 'error', 'message': '没有重复记录'})
        
        # 删除除第一条外的所有记录
        for record in records[1:]:
            db.session.delete(record)
        
        db.session.commit()
        return jsonify({'status': 'success'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/merge_records')
def merge_records():
    """合并IP范围相同的记录"""
    # 查找IP范围相同的记录组
    merge_groups = db.session.query(
        FYIP.ipBegin,
        FYIP.ipEnd,
        db.func.count(FYIP.id).label('count'),
        db.func.group_concat(FYIP.id).label('ids'),
        db.func.group_concat(db.func.nullif(FYIP.name, '')).label('names'),
        db.func.group_concat(db.func.nullif(FYIP.address, '')).label('addresses'),
        db.func.group_concat(db.func.nullif(FYIP.group, '')).label('groups'),
        db.func.group_concat(db.func.nullif(FYIP.isp, '')).label('isps')
    ).group_by(
        FYIP.ipBegin,
        FYIP.ipEnd
    ).having(
        db.func.count(FYIP.id) > 1
    ).order_by(
        FYIP.ipBegin,
        FYIP.ipEnd
    ).all()
    
    return render_template('merge_records.html',
                         merge_groups=merge_groups,
                         get_detail_records=get_detail_records)

def get_detail_records(ids):
    """获取指定ID列表的详细记录"""
    if isinstance(ids, str):
        id_list = [int(id) for id in ids.split(',')]
    else:
        id_list = ids
    return FYIP.query.filter(FYIP.id.in_(id_list)).order_by(FYIP.id).all()

@ip_bp.route('/do_merge', methods=['POST'])
def do_merge():
    """执行记录合并操作"""
    try:
        # 查找所有需要合并的组
        merge_groups = db.session.query(
            FYIP.ipBegin,
            FYIP.ipEnd,
            db.func.group_concat(FYIP.id).label('ids'),
            db.func.group_concat(db.func.nullif(FYIP.name, '')).label('names'),
            db.func.group_concat(db.func.nullif(FYIP.address, '')).label('addresses'),
            db.func.group_concat(db.func.nullif(FYIP.group, '')).label('groups'),
            db.func.group_concat(db.func.nullif(FYIP.isp, '')).label('isps')
        ).group_by(
            FYIP.ipBegin,
            FYIP.ipEnd
        ).having(
            db.func.count(FYIP.id) > 1
        ).all()
        
        for group in merge_groups:
            # 获取该组所有记录
            records = get_detail_records(group.ids)
            if len(records) < 2:
                continue
                
            # 使用第一条记录作为基础
            base_record = records[0]
            
            # 更新合并后的信息
            base_record.name = group.names
            base_record.address = group.addresses
            base_record.group = group.groups
            base_record.isp = group.isps
            
            # 删除其他记录
            for record in records[1:]:
                db.session.delete(record)
        
        db.session.commit()
        return jsonify({'status': 'success'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/ip_stats', methods=['GET', 'POST'])
def ip_stats():
    """IP分组实际网段范围统计"""
    # 获取所有ISP
    isps = db.session.query(FYIP.isp).distinct().order_by(FYIP.isp).all()
    
    # 获取选中的ISP
    selected_isps = request.form.getlist('isps[]') if request.method == 'POST' else []
    
    # 构建基础查询
    base_query = db.session.query(FYIP.group).distinct()
    if selected_isps:
        base_query = base_query.filter(FYIP.isp.in_(selected_isps))
    groups = base_query.order_by(FYIP.group).all()
    
    group_stats = []
    for group in groups:
        group_name = group[0]
        if not group_name:
            continue
            
        # 获取该组的所有IP范围
        ranges_query = db.session.query(FYIP.ipBegin, FYIP.ipEnd)\
            .filter(FYIP.group == group_name)
        if selected_isps:
            ranges_query = ranges_query.filter(FYIP.isp.in_(selected_isps))
        ranges = ranges_query.order_by(FYIP.ipBegin).all()
        
        if not ranges:
            continue
            
        # 合并IP范围
        merged_networks = []
        current = None
        
        for range_item in ranges:
            if current is None:
                current = {
                    'start': range_item.ipBegin,
                    'end': range_item.ipEnd
                }
            else:
                if range_item.ipBegin <= current['end'] + 1:
                    # 合并重叠范围
                    current['end'] = max(current['end'], range_item.ipEnd)
                else:
                    # 计算当前范围的最优CIDR
                    cidr = calculate_optimal_cidr(current['start'], current['end'])
                    merged_networks.append({
                        'start_ip': int_to_ip(current['start']),
                        'end_ip': int_to_ip(current['end']),
                        'cidr': f"{int_to_ip(cidr['network'])}/{cidr['bits']}",
                        'ip_count': current['end'] - current['start'] + 1
                    })
                    current = {
                        'start': range_item.ipBegin,
                        'end': range_item.ipEnd
                    }
        
        # 处理最后一个范围
        if current:
            cidr = calculate_optimal_cidr(current['start'], current['end'])
            merged_networks.append({
                'start_ip': int_to_ip(current['start']),
                'end_ip': int_to_ip(current['end']),
                'cidr': f"{int_to_ip(cidr['network'])}/{cidr['bits']}",
                'ip_count': current['end'] - current['start'] + 1
            })
        
        # 计算总IP数量
        total_ips = sum(network['ip_count'] for network in merged_networks)
        
        group_stats.append({
            'name': group_name,
            'network_count': len(merged_networks),
            'total_ips': total_ips,
            'networks': merged_networks
        })
    
    return render_template('ip_stats.html',
                         isps=isps,
                         selected_isps=selected_isps,
                         group_stats=group_stats)

def calculate_optimal_cidr(start_ip, end_ip):
    """计算最优CIDR表示"""
    # 如果开始IP和结束IP相同，返回/32
    if start_ip == end_ip:
        return {
            'network': start_ip,
            'bits': 32
        }
    
    # 计算需要的比特数
    diff = end_ip - start_ip + 1
    bits = 32 - (diff - 1).bit_length()
    
    # 创建掩码
    mask = (0xFFFFFFFF << (32 - bits)) & 0xFFFFFFFF  # 确保是32位无符号整数
    
    # 计算网络地址
    network = start_ip & mask
    broadcast = network | (~mask & 0xFFFFFFFF)
    
    # 检查是否是有效的CIDR块
    if network <= start_ip and broadcast >= end_ip:
        return {
            'network': network,
            'bits': bits
        }
    
    # 如果不能完美匹配，则使用下一个更小的掩码
    bits -= 1
    mask = (0xFFFFFFFF << (32 - bits)) & 0xFFFFFFFF  # 确保是32位无符号整数
    return {
        'network': start_ip & mask,
        'bits': bits
    }

@ip_bp.app_template_filter('number_format')
def number_format(value):
    """格式化数字，添加千位分隔符"""
    return "{:,}".format(value)



@ip_bp.route('/merge_ip_ranges', methods=['GET', 'POST'])
def merge_ip_ranges():
    """IP段合并工具"""
    if request.method == 'POST' and request.form.get('ip_ranges'):
        ip_ranges = request.form.get('ip_ranges').strip()
        if not ip_ranges:
            return render_template('merge_ip_ranges.html')
            
        lines = ip_ranges.split('\n')
        errors = []
        ranges = []
        
        # 验证并解析每一行
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            # 验证格式
            validation_result = validate_ip_format(line, i)
            if validation_result is not True:
                errors.append(validation_result)
                continue
                
            # 解析IP范围
            ip_range = parse_ip_range(line)
            if ip_range:
                ranges.append(ip_range)
        
        if errors:
            return render_template('merge_ip_ranges.html', errors=errors)
            
        # 排序并合并重叠的范围
        ranges.sort(key=lambda x: x[0])
        merged = merge_ranges(ranges)
        
        # 生成不同格式的结果
        result = {
            'cidrs': generate_cidrs(merged),
            'subnets': generate_subnets(merged),
            'ranges': generate_ranges(merged)
        }
        
        return render_template('merge_ip_ranges.html', merged_ranges=result)
        
    return render_template('merge_ip_ranges.html')

def validate_ip_format(line, line_number):
    """验证IP格式"""
    # 子网掩码格式: *********** *************
    if ' ' in line:
        ip, mask = line.split(' ', 1)
        if not is_valid_ip(ip) or not is_valid_mask(mask):
            return f"第 {line_number} 行格式错误: {line}"
        return True
        
    # CIDR格式: ***********/24
    if '/' in line:
        ip, bits = line.split('/', 1)
        if not is_valid_ip(ip) or not bits.isdigit() or not 0 <= int(bits) <= 32:
            return f"第 {line_number} 行格式错误: {line}"
        return True
        
    # 范围格式: ***********-*************
    if '-' in line:
        start, end = line.split('-', 1)
        if '.' not in end:  # 简写格式
            start_parts = start.split('.')
            if len(start_parts) != 4:
                return f"第 {line_number} 行格式错误: {line}"
            end = '.'.join(start_parts[:-1] + [end])
        if not is_valid_ip(start) or not is_valid_ip(end):
            return f"第 {line_number} 行格式错误: {line}"
        if ip_to_int(start) > ip_to_int(end):
            return f"第 {line_number} 行IP范围起始地址大于结束地址: {line}"
        return True
        
    # 单个IP
    if not is_valid_ip(line):
        return f"第 {line_number} 行格式错误: {line}"
    return True

def is_valid_ip(ip):
    """验证IP地址格式"""
    try:
        parts = ip.split('.')
        if len(parts) != 4:
            return False
        return all(part.isdigit() and 0 <= int(part) <= 255 for part in parts)
    except:
        return False

def is_valid_mask(mask):
    """验证子网掩码格式"""
    if not is_valid_ip(mask):
        return False
    # 验证掩码是否有效（必须是连续的1后跟连续的0）
    mask_int = ip_to_int(mask)
    found_zero = False
    for i in range(31, -1, -1):
        bit = (mask_int >> i) & 1
        if found_zero and bit == 1:
            return False
        if bit == 0:
            found_zero = True
    return True

def parse_ip_range(line):
    """解析IP范围"""
    line = line.strip()
    if not line:
        return None
        
    # 子网掩码格式
    if ' ' in line:
        ip, mask = line.split(' ', 1)
        ip_int = ip_to_int(ip)
        mask_int = ip_to_int(mask)
        network = ip_int & mask_int
        broadcast = network | (~mask_int & 0xFFFFFFFF)
        return (network, broadcast)
        
    # CIDR格式
    if '/' in line:
        ip, bits = line.split('/', 1)
        ip_int = ip_to_int(ip)
        bits = int(bits)
        mask = 0xFFFFFFFF << (32 - bits)
        network = ip_int & mask
        broadcast = network | (~mask & 0xFFFFFFFF)
        return (network, broadcast)
        
    # 范围格式
    if '-' in line:
        start, end = line.split('-', 1)
        if '.' not in end:  # 简写格式
            start_parts = start.split('.')
            end = '.'.join(start_parts[:-1] + [end])
        return (ip_to_int(start), ip_to_int(end))
        
    # 单个IP
    return (ip_to_int(line), ip_to_int(line))

def merge_ranges(ranges):
    """合并重叠的IP范围"""
    if not ranges:
        return []
        
    merged = []
    current = ranges[0]
    
    for range_item in ranges[1:]:
        if range_item[0] <= current[1] + 1:
            current = (current[0], max(current[1], range_item[1]))
        else:
            merged.append(current)
            current = range_item
            
    merged.append(current)
    return merged

def generate_cidrs(ranges):
    """生成CIDR格式结果"""
    cidrs = []
    for start, end in ranges:
        # 使用改进的CIDR计算
        sub_cidrs = split_to_cidrs(start, end)
        for cidr in sub_cidrs:
            network = int_to_ip(cidr['network'])
            ip_count = 2 ** (32 - cidr['bits'])
            cidrs.append({
                'network': network,
                'bits': cidr['bits'],
                'ip_count': ip_count
            })
    return cidrs

def generate_subnets(ranges):
    """生成子网掩码格式结果"""
    subnets = []
    for start, end in ranges:
        sub_cidrs = split_to_cidrs(start, end)
        for cidr in sub_cidrs:
            network = int_to_ip(cidr['network'])
            # 修复掩码计算
            mask_int = (0xFFFFFFFF << (32 - cidr['bits'])) & 0xFFFFFFFF  # 确保是32位无符号整数
            mask = int_to_ip(mask_int)
            ip_count = 2 ** (32 - cidr['bits'])
            subnets.append({
                'network': network,
                'mask': mask,
                'ip_count': ip_count
            })
    return subnets

def generate_ranges(ranges):
    """生成IP范围格式结果"""
    return [{
        'start': int_to_ip(start),
        'end': int_to_ip(end),
        'ip_count': end - start + 1
    } for start, end in ranges]

def split_to_cidrs(start, end):
    """将IP范围分割为CIDR块"""
    cidrs = []
    while start <= end:
        # 从最大的掩码开始尝试(/32)，找到能包含当前起始IP的最大CIDR块
        max_size = 0
        best_prefix = 32
        best_network = start
        
        # 尝试从/32到/0的每个掩码
        for prefix in range(32, -1, -1):
            mask = (0xFFFFFFFF << (32 - prefix)) & 0xFFFFFFFF  # 确保是32位无符号整数
            network = start & mask
            broadcast = network | (~mask & 0xFFFFFFFF)
            
            # 检查这个CIDR块是否完全在范围内
            if network >= start and broadcast <= end:
                size = broadcast - network + 1
                if size > max_size:
                    max_size = size
                    best_prefix = prefix
                    best_network = network
        
        # 添加找到的最佳CIDR块
        cidrs.append({
            'network': best_network,
            'bits': best_prefix
        })
        
        # 移动到下一个未覆盖的IP
        mask = (0xFFFFFFFF << (32 - best_prefix)) & 0xFFFFFFFF  # 确保是32位无符号整数
        broadcast = best_network | (~mask & 0xFFFFFFFF)
        start = broadcast + 1
        
    return cidrs

@ip_bp.route('/group_by_region', methods=['GET', 'POST'])
def group_by_region():
    # 获取所有ISP
    isps = db.session.query(FYIP.isp).distinct()
    isps = [{'isp': isp[0] if isp[0] else '未知ISP'} for isp in isps]
    
    # 获取选中的ISP
    selected_isps = request.form.getlist('isps[]') if request.method == 'POST' else []
    
    # 获取所有国家
    countries = [c[0] for c in db.session.query(FYIP.country).distinct()
                                       .filter(FYIP.country.isnot(None))
                                       .filter(FYIP.country != '')
                                       .order_by(FYIP.country)]
    
    if request.method == 'POST' and 'do_group' in request.form:
        country = request.form.get('country')
        province = request.form.get('province')
        city = request.form.get('city')
        county = request.form.get('county')
        
        # 构建查询条件
        conditions = []
        if country: conditions.append(FYIP.country == country)
        if province: conditions.append(FYIP.province == province)
        if city: conditions.append(FYIP.city == city)
        if county: conditions.append(FYIP.county == county)
    
    # 添加ISP过滤条件
    if selected_isps:
        isp_conditions = []
        for isp in selected_isps:
            if isp == '未知ISP':
                    isp_conditions.append(db.or_(FYIP.isp.is_(None), FYIP.isp == ''))
            else:
                isp_conditions.append(FYIP.isp == isp)
            conditions.append(db.or_(*isp_conditions))
        
        if conditions:
            # 获取符合条件的IP段
            query = db.session.query(FYIP.ipBegin, FYIP.ipEnd).filter(*conditions)
            networks = [{'start': row[0], 'end': row[1]} for row in query]
            
            if networks:
                # 合并重叠的网段并计算CIDR
                merged_networks = merge_ip_ranges(networks)
                total_ips = sum(net['ip_count'] for net in merged_networks)
                
                return render_template('group_by_region.html',
                    isps=isps,
                    selected_isps=selected_isps,
                    countries=countries,
                    selected_country=country,
                    selected_province=province,
                    selected_city=city,
                    selected_county=county,
                    merged_networks=merged_networks,
                    total_ips=total_ips
                )
            else:
                error = "未找到匹配的记录"
        else:
            error = "请至少选择一个地区条件"
            
        return render_template('group_by_region.html',
            isps=isps,
            selected_isps=selected_isps,
            countries=countries,
            error=error
        )
    
    return render_template('group_by_region.html',
        isps=isps,
        selected_isps=selected_isps,
        countries=countries
    )

def merge_ip_ranges(networks):
    """合并重叠的IP段并计算CIDR"""
    if not networks:
        return []
    
    # 按起始IP排序
    networks.sort(key=lambda x: x['start'])
    
    merged = []
    current = networks[0].copy()
    
    for network in networks[1:]:
        if current['end'] + 1 >= network['start']:
            # 合并重叠的网段
            current['end'] = max(current['end'], network['end'])
        else:
            # 处理当前网段
            process_network(current)
            merged.append(current)
            current = network.copy()
    
    # 处理最后一个网段
    process_network(current)
    merged.append(current)
    
    return merged

def process_network(network):
    """处理单个网段，添加IP地址显示和CIDR计算"""
    network['start_ip'] = socket.inet_ntoa(struct.pack('!L', network['start']))
    network['end_ip'] = socket.inet_ntoa(struct.pack('!L', network['end']))
    network['ip_count'] = network['end'] - network['start'] + 1
    network['cidrs'] = calculate_optimal_cidrs(network['start'], network['end'])

def calculate_optimal_cidrs(start_ip, end_ip):
    """计算最优CIDR表示"""
    cidrs = []
    while start_ip <= end_ip:
        max_size = 0
        best_prefix = 32
        
        for prefix in range(32, -1, -1):
            mask = 0xffffffff << (32 - prefix)
            network = start_ip & mask
            broadcast = network | (~mask & 0xffffffff)
            
            if network == start_ip and broadcast <= end_ip:
                size = 1 << (32 - prefix)
                if size > max_size:
                    max_size = size
                    best_prefix = prefix
        
        if max_size > 0:
            mask = 0xffffffff << (32 - best_prefix)
            network = start_ip & mask
            cidrs.append({
                'network': socket.inet_ntoa(struct.pack('!L', network)),
                'prefix': best_prefix
            })
            start_ip = network + max_size
        else:
            start_ip += 1
            
    return cidrs

@ip_bp.route('/get_regions')
def get_regions():
    try:
        type = request.args.get('type')
        if not type:
            return jsonify({'error': 'type 参数是必需的'}), 400
            
        if type not in ['province', 'city', 'county']:
            return jsonify({'error': f'无效的 type 参数: {type}'}), 400
            
        parent = request.args.get('parent')
        
        query = db.session.query(getattr(FYIP, type)).distinct()
        
        # 添加父级过滤条件
        if parent:
            parent_field = {
                'province': FYIP.country,
                'city': FYIP.province,
                'county': FYIP.city
            }.get(type)
            
            if parent_field:
                query = query.filter(parent_field == parent)
        
        # 过滤空值并排序
        regions = [r[0] for r in query.filter(getattr(FYIP, type).isnot(None))
                              .filter(getattr(FYIP, type) != '')
                              .order_by(getattr(FYIP, type))]
                              
        # 确保返回的是有效的JSON数据
        return jsonify({'data': regions, 'status': 'success'})
        
    except AttributeError as e:
        print(f'数据库字段错误: {str(e)}')
        return jsonify({'error': '数据库字段错误', 'status': 'error'}), 500
    except Exception as e:
        print(f'get_regions 发生错误: {str(e)}')
        return jsonify({'error': '服务器内部错误', 'status': 'error'}), 500



@ip_bp.route('/check_groups')
def check_groups():
    """分组信息检查与修改"""
    # 获取所有不同的分组
    groups = db.session.query(FYIP.group).distinct().all()
    return render_template('check_groups.html', groups=groups)

@ip_bp.route('/check_region', methods=['GET', 'POST'])
def check_region():
    try:
        # 处理批量更新请求
        if request.method == 'POST':
            action = request.form.get('action')
            if action == 'batch_update':
                try:
                    # 获取要更新的字段和记录ID
                    fields = request.form.getlist('fields[]')
                    rows = request.form.getlist('rows[]')
                    
                    print("接收到的表单数据:")
                    for key in request.form:
                        print(f"{key}: {request.form.getlist(key)}")
                    
                    if not fields or not rows:
                        return jsonify({
                            'status': 'error',
                            'message': '未选择要更新的字段或记录'
                        })
                    
                    # 更新记录
                    updated_count = 0
                    for row_id in rows:
                        record = FYIP.query.get(row_id)
                        if record:
                            print(f"\n更新记录 {row_id}:")
                            for field in fields:
                                if hasattr(record, field):
                                    old_value = getattr(record, field)
                                    value = request.form.get(field, '')
                                    setattr(record, field, value)
                                    print(f"  {field}: {old_value} -> {value}")
                            updated_count += 1
                    
                    db.session.commit()
                    return jsonify({
                        'status': 'success',
                        'message': f'成功更新 {updated_count} 条记录'
                    })
                    
                except Exception as e:
                    db.session.rollback()
                    print(f"批量更新错误: {str(e)}")
                    return jsonify({
                        'status': 'error',
                        'message': f'更新失败: {str(e)}'
                    })
        
        # 处理GET请求
        page = request.args.get('page', 1, type=int)
        ip_range_search = request.args.get('ip_range_search', '')
        
        query = FYIP.query
        display_format = None  # 用于显示的IP范围格式
        
        # 如果有IP范围搜索
        if ip_range_search:
            try:
                # 处理IP范围搜索
                if '-' in ip_range_search:
                    start_ip, end_ip = ip_range_search.split('-')
                    start_ip = ip_to_int(start_ip.strip())
                    end_ip = ip_to_int(end_ip.strip())
                    display_format = ip_range_search  # 已经是范围格式，直接使用
                else:
                    # 如果只输入了单个IP或CIDR
                    if '/' in ip_range_search:
                        # CIDR格式
                        network = ipaddress.ip_network(ip_range_search.strip(), strict=False)
                        start_ip = int(network[0])
                        end_ip = int(network[-1])
                        # 转换为范围格式显示
                        display_format = f"{network[0]}-{network[-1]}"
                    else:
                        # 单个IP
                        ip = ip_to_int(ip_range_search.strip())
                        start_ip = end_ip = ip
                        display_format = ip_range_search

                # 添加调试日志
                print(f"搜索IP范围: start_ip={start_ip}, end_ip={end_ip}")
                
                # 修改查询条件
                query = query.filter(
                    db.and_(
                        FYIP.ipBegin <= end_ip,
                        FYIP.ipEnd >= start_ip
                    )
                )
                
            except ValueError as e:
                print(f"IP转换错误: {str(e)}")
                flash(f'IP格式错误: {str(e)}', 'error')
                return redirect(url_for('ip.check_region'))
            except Exception as e:
                print(f"IP范围处理错误: {str(e)}")
                flash(f'IP范围处理错误: {str(e)}', 'error')
                return redirect(url_for('ip.check_region'))

        # 分页
        per_page = 50
        try:
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )
            
            # 添加调试日志
            print(f"查询结果数: {pagination.total}")
            
            return render_template('check_region.html',
                                records=pagination.items,
                                pagination=pagination,
                                ip_range_search=ip_range_search,  # 原始输入格式，用于搜索框
                                display_format=display_format)    # 转换后的范围格式，用于显示
                                
        except Exception as e:
            print(f"分页查询错误: {str(e)}")
            db.session.rollback()
            flash('查询出错，请重试', 'error')
            return redirect(url_for('ip.check_region'))
            
    except Exception as e:
        print(f"check_region整体错误: {str(e)}")
        db.session.rollback()
        flash('系统错误，请重试', 'error')
        return redirect(url_for('ip.check_region'))

@ip_bp.route('/update_field', methods=['POST'])
def update_field():
    """更新单个字段"""
    try:
        record_id = request.form.get('id')
        field = request.form.get('field')
        value = request.form.get('value')
        
        record = FYIP.query.get_or_404(record_id)
        
        # 特殊字段处理
        if field in ['ipBegin', 'ipEnd']:
            value = ip_to_int(value)
        elif field == 'mac':
            if value:
                value = bytes.fromhex(value.replace(':', '').replace('-', ''))
            else:
                value = None
        elif field in ['longitude', 'latitude']:
            value = float(value) if value else None
        elif field == 'isused':
            value = int(value)
        
        setattr(record, field, value)
        db.session.commit()
        
        return jsonify({'status': 'success'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/traceroute/<int:ip_int>/<int:fyip_id>', methods=['POST'])
def do_traceroute(ip_int, fyip_id):
    """执行ICMP traceroute并保存结果"""
    try:
        # 删除同一目标IP和fyip_id的ICMP跟踪记录
        db.session.execute(
            """DELETE FROM traceroute_records 
               WHERE target_ip = :target_ip 
               AND fyip_id = :fyip_id
               AND trace_type = 'ICMP'""",  # 只删除ICMP类型的记录
            {'target_ip': ip_int, 'fyip_id': fyip_id}
        )
        db.session.commit()

        # 将整数IP转换为点分十进制格式
        target_ip = int_to_ip(ip_int)
        max_hops = 10  # 最大跳数限制为10跳
        results = []
        
        # 记录跟踪开始时间
        trace_time = datetime.now()

        for ttl in range(1, max_hops + 1):
            # 构造和发送数据包
            pkt = IP(dst=target_ip, ttl=ttl) / ICMP()
            start_time = time.time()
            reply = sr1(pkt, timeout=2, verbose=0)
            rtt = (time.time() - start_time) * 1000  # 转换为毫秒

            if reply is None:
                # 超时的情况，记录为*
                trace_record = {
                    'target_ip': ip_int,
                    'hop_number': ttl,
                    'hop_ip': 0,  # 用0表示超时
                    'rtt_ms': 0.0,  # 超时时RTT记为0
                    'trace_time': trace_time,  # 每一跳都记录相同的时间
                    'fyip_id': fyip_id,  # 添加fyip_id
                    'trace_type': 'ICMP'  # 添加跟踪类型
                }
                
                # 插入记录
                try:
                    db.session.execute(
                        """INSERT INTO traceroute_records 
                           (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                           VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                        trace_record
                    )
                    db.session.commit()
                    results.append({
                        'hop': ttl,
                        'ip': '*',  # 显示为星号
                        'rtt': 0
                    })
                except Exception as e:
                    db.session.rollback()
                    return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})
                
                continue

            # 有响应的情况
            hop_ip = ip_to_int(reply.src)
            
            # 保存到数据库
            trace_record = {
                'target_ip': ip_int,
                'hop_number': ttl,
                'hop_ip': hop_ip,
                'rtt_ms': rtt,
                'trace_time': trace_time,
                'fyip_id': fyip_id,
                'trace_type': 'ICMP'  # 添加跟踪类型
            }
            
            try:
                db.session.execute(
                    """INSERT INTO traceroute_records 
                       (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                       VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                    trace_record
                )
                db.session.commit()
                results.append({
                    'hop': ttl,
                    'ip': reply.src,
                    'rtt': round(rtt, 2)
                })
            except Exception as e:
                db.session.rollback()
                return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})

            # 如果到达目标IP，结束跟踪
            if reply.src == target_ip:
                break

        return jsonify({
            'status': 'success',
            'message': '跟踪完成',
            'results': results,
            'target_ip': target_ip,  # 添加目标IP
            'trace_time': trace_time.strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/find_same_hops', methods=['GET'])
def find_same_hops():
    """查找具有相同跳跃路径的记录，不区分跟踪类型"""
    try:
        # 使用SQLite兼容的语法
        latest_traces = """
        WITH OrderedHops AS (
            -- 先对hop_ip进行排序
            SELECT 
                fyip_id,
                trace_time,
                hop_number,
                CASE 
                    WHEN hop_ip = 0 THEN '*' 
                    ELSE CAST(hop_ip AS TEXT) 
                END as hop_value
            FROM traceroute_records
            ORDER BY fyip_id, trace_time, hop_number
        ),
        PathSequences AS (
            -- 然后进行分组拼接，不包含trace_type
            SELECT 
                fyip_id,
                trace_time,
                GROUP_CONCAT(hop_value) as path
            FROM OrderedHops
            GROUP BY fyip_id, trace_time
        ),
        -- 找出最新的路径记录
        LatestPaths AS (
            SELECT 
                fyip_id,
                path,
                MAX(trace_time) as latest_time
            FROM PathSequences
            GROUP BY fyip_id
        ),
        -- 找出具有相同路径的记录
        SimilarPaths AS (
            SELECT 
                path,
                GROUP_CONCAT(fyip_id) as fyip_ids,
                COUNT(*) as path_count
            FROM LatestPaths
            GROUP BY path
            HAVING COUNT(*) > 1
        )
        -- 获取相关的IP范围信息
        SELECT 
            sp.path,
            sp.fyip_ids,
            sp.path_count,
            GROUP_CONCAT(f.name) as names,
            GROUP_CONCAT(f.address) as addresses
        FROM SimilarPaths sp
        INNER JOIN fyip f ON f.id IN (
            SELECT CAST(value AS INTEGER)
            FROM json_each('["' || REPLACE(sp.fyip_ids, ',', '","') || '"]')
        )
        GROUP BY sp.path, sp.fyip_ids, sp.path_count
        """
        
        results = db.session.execute(latest_traces).fetchall()
        
        if not results:
            return jsonify({
                'status': 'success',
                'data': []
            })

        similar_routes = []
        for row in results:
            # 将路径字符串转换为IP地址列表
            hop_ips = []
            for hop_ip in row.path.split(','):
                if hop_ip == '*':
                    hop_ips.append('*')
                else:
                    try:
                        hop_ips.append(int_to_ip(int(hop_ip)))
                    except:
                        hop_ips.append('无效IP')
            
            # 获取相关的IP范围记录
            fyip_ids = [int(id) for id in row.fyip_ids.split(',')]
            ip_records = FYIP.query.filter(FYIP.id.in_(fyip_ids)).all()
            
            # 将FYIP对象转换为字典
            ip_records_dict = []
            for record in ip_records:
                ip_records_dict.append({
                    'id': record.id,
                    'ipBegin': record.ipBegin,
                    'ipEnd': record.ipEnd,
                    'name': record.name or '',
                    'address': record.address or '',
                    'group': record.group or '',
                    'isp': record.isp or ''
                })
            
            similar_routes.append({
                'hop_sequence': hop_ips,
                'ip_records': ip_records_dict,
                'record_count': row.path_count,
                'names': row.names.split(',') if row.names else [],
                'addresses': row.addresses.split(',') if row.addresses else []
            })
        
        return jsonify({
            'status': 'success',
            'data': similar_routes
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@ip_bp.route('/udp_traceroute/<int:ip_int>/<int:fyip_id>', methods=['POST'])
def do_udp_traceroute(ip_int, fyip_id):
    """执行UDP traceroute并保存结果"""
    try:
        # 删除同一目标IP和fyip_id的UDP跟踪记录
        db.session.execute(
            """DELETE FROM traceroute_records 
               WHERE target_ip = :target_ip 
               AND fyip_id = :fyip_id
               AND trace_type = 'UDP'""",  # 只删除UDP类型的记录
            {'target_ip': ip_int, 'fyip_id': fyip_id}
        )
        db.session.commit()

        # 将整数IP转换为点分十进制格式
        target_ip = int_to_ip(ip_int)
        max_hops = 10  # 最大跳数限制为10跳
        results = []
        
        # 记录跟踪开始时间
        trace_time = datetime.now()

        for ttl in range(1, max_hops + 1):
            # 构造和发送UDP数据包
            pkt = IP(dst=target_ip, ttl=ttl) / UDP(dport=33434)
            start_time = time.time()
            reply = sr1(pkt, timeout=2, verbose=0)
            rtt = (time.time() - start_time) * 1000  # 转换为毫秒

            if reply is None:
                # 超时的情况，记录为*
                trace_record = {
                    'target_ip': ip_int,
                    'hop_number': ttl,
                    'hop_ip': 0,  # 用0表示超时
                    'rtt_ms': 0.0,  # 超时时RTT记为0
                    'trace_time': trace_time,  # 每一跳都记录相同的时间
                    'fyip_id': fyip_id,  # 添加fyip_id
                    'trace_type': 'UDP'  # 添加跟踪类型
                }
                
                # 插入记录
                try:
                    db.session.execute(
                        """INSERT INTO traceroute_records 
                           (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                           VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                        trace_record
                    )
                    db.session.commit()
                    results.append({
                        'hop': ttl,
                        'ip': '*',  # 显示为星号
                        'rtt': 0
                    })
                except Exception as e:
                    db.session.rollback()
                    return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})
                
                continue

            # 有响应的情况
            hop_ip = ip_to_int(reply.src)
            
            # 保存到数据库
            trace_record = {
                'target_ip': ip_int,
                'hop_number': ttl,
                'hop_ip': hop_ip,
                'rtt_ms': rtt,
                'trace_time': trace_time,
                'fyip_id': fyip_id,
                'trace_type': 'UDP'  # 添加跟踪类型
            }
            
            try:
                db.session.execute(
                    """INSERT INTO traceroute_records 
                       (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                       VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                    trace_record
                )
                db.session.commit()
                results.append({
                    'hop': ttl,
                    'ip': reply.src,
                    'rtt': round(rtt, 2)
                })
            except Exception as e:
                db.session.rollback()
                return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})

            # 如果收到ICMP端口不可达消息，说明到达目标
            if reply.haslayer(ICMP) and reply[ICMP].type == 3 and reply[ICMP].code == 3:
                break

        return jsonify({
            'status': 'success',
            'message': 'UDP跟踪完成',
            'results': results,
            'target_ip': target_ip,
            'trace_time': trace_time.strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/tcp_traceroute/<int:ip_int>/<int:fyip_id>', methods=['POST'])
def do_tcp_traceroute(ip_int, fyip_id):
    """执行TCP traceroute并保存结果"""
    try:
        # 删除同一目标IP和fyip_id的TCP跟踪记录
        db.session.execute(
            """DELETE FROM traceroute_records 
               WHERE target_ip = :target_ip 
               AND fyip_id = :fyip_id
               AND trace_type = 'TCP'""",  # 只删除TCP类型的记录
            {'target_ip': ip_int, 'fyip_id': fyip_id}
        )
        db.session.commit()

        # 将整数IP转换为点分十进制格式
        target_ip = int_to_ip(ip_int)
        max_hops = 10  # 最大跳数限制为10跳
        results = []
        
        # 记录跟踪开始时间
        trace_time = datetime.now()

        for ttl in range(1, max_hops + 1):
            # 构造和发送TCP SYN数据包
            pkt = IP(dst=target_ip, ttl=ttl) / TCP(dport=80, flags="S")
            start_time = time.time()
            reply = sr1(pkt, timeout=2, verbose=0)
            rtt = (time.time() - start_time) * 1000  # 转换为毫秒

            if reply is None:
                # 超时的情况，记录为*
                trace_record = {
                    'target_ip': ip_int,
                    'hop_number': ttl,
                    'hop_ip': 0,  # 用0表示超时
                    'rtt_ms': 0.0,  # 超时时RTT记为0
                    'trace_time': trace_time,  # 每一跳都记录相同的时间
                    'fyip_id': fyip_id,  # 添加fyip_id
                    'trace_type': 'TCP'  # 添加跟踪类型
                }
                
                # 插入记录
                try:
                    db.session.execute(
                        """INSERT INTO traceroute_records 
                           (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                           VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                        trace_record
                    )
                    db.session.commit()
                    results.append({
                        'hop': ttl,
                        'ip': '*',  # 显示为星号
                        'rtt': 0
                    })
                except Exception as e:
                    db.session.rollback()
                    return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})
                
                continue

            # 有响应的情况
            hop_ip = ip_to_int(reply.src)
            
            # 保存到数据库
            trace_record = {
                'target_ip': ip_int,
                'hop_number': ttl,
                'hop_ip': hop_ip,
                'rtt_ms': rtt,
                'trace_time': trace_time,
                'fyip_id': fyip_id,
                'trace_type': 'TCP'  # 添加跟踪类型
            }
            
            try:
                db.session.execute(
                    """INSERT INTO traceroute_records 
                       (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                       VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                    trace_record
                )
                db.session.commit()
                results.append({
                    'hop': ttl,
                    'ip': reply.src,
                    'rtt': round(rtt, 2)
                })
            except Exception as e:
                db.session.rollback()
                return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})

            # 如果收到TCP RST或者到达目标IP，结束跟踪
            if reply.haslayer(TCP) and (reply[TCP].flags & 0x04) or reply.src == target_ip:
                break

        return jsonify({
            'status': 'success',
            'message': 'TCP跟踪完成',
            'results': results,
            'target_ip': target_ip,
            'trace_time': trace_time.strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/route_analysis')
def route_analysis():
    """路由分析页面"""
    return render_template('route_analysis.html') 