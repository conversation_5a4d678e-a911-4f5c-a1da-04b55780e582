from app import db

class FYIP(db.Model):
    __tablename__ = 'fyip'
    
    id = db.Column(db.Integer, primary_key=True)
    ipBegin = db.Column(db.Integer, nullable=False)
    ipEnd = db.Column(db.Integer, nullable=False)
    name = db.Column(db.String(100))
    group = db.Column(db.String(100))
    user = db.Column(db.String(100))
    type = db.Column(db.String(100))
    system = db.Column(db.String(100))
    mac = db.Column(db.LargeBinary)
    manufacturer = db.Column(db.String(100))
    remark = db.Column(db.Text)
    port = db.Column(db.String(100))
    longitude = db.Column(db.Float)
    latitude = db.Column(db.Float)
    country = db.Column(db.String(100))
    province = db.Column(db.String(100))
    city = db.Column(db.String(100))
    county = db.Column(db.String(100))
    address = db.Column(db.String(200))
    isp = db.Column(db.String(100))
    isused = db.Column(db.Integer, default=1)
    firsttime = db.Column(db.DateTime)
    lasttime = db.Column(db.DateTime)

# QQWryIP 和 NetIP 模型已删除