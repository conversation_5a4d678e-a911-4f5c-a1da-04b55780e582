from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app, send_file
from app.models.ip import FYIP
from app import db
from app.utils.helpers import int_to_ip, format_mac, ip_to_int
import socket
import struct
import ipaddress
from datetime import datetime
import time
import os
import io
import csv
import chardet
from app.utils.port_scanner import PortScanner
import dns.resolver
import sqlite3
import json
import threading
import subprocess
import queue
import re

# 尝试导入 scapy，如果失败则使用替代方案
try:
    from scapy.all import IP, ICMP, sr1, UDP, TCP
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False
    # 定义空的替代类，避免导入错误
    class IP:
        def __init__(self, **kwargs):
            pass
        def __truediv__(self, other):
            return self
    class ICMP:
        def __init__(self, **kwargs):
            pass
    class UDP:
        def __init__(self, **kwargs):
            pass
    class TCP:
        def __init__(self, **kwargs):
            pass
    def sr1(*args, **kwargs):
        return None

# QQwry 相关功能已删除
QQWRY_AVAILABLE = False

# QQwry 相关代码已删除

ip_bp = Blueprint('ip', __name__)

# 添加max和min函数到Jinja2模板环境
@ip_bp.app_template_global()
def max_value(*args):
    return max(args)

@ip_bp.app_template_global()
def min_value(*args):
    return min(args)

@ip_bp.route('/')
def index():
    page = request.args.get('page', 1, type=int)
    pagesize = 40
    
    pagination = FYIP.query.order_by(FYIP.ipBegin).paginate(
        page=page, per_page=pagesize, error_out=False)
    
    return render_template('index.html',
                         records=pagination.items,
                         page=page,
                         recordcount=FYIP.query.count(),
                         pagecount=pagination.pages)

@ip_bp.route('/add', methods=['GET'])
def add_form():
    return render_template('add.html')

@ip_bp.route('/add', methods=['POST'])
def add_record():
    form_data = request.form.to_dict()  # 保存表单数据
    try:
        # 获取表单数据
        ip_begin = form_data.get('ipBegin')
        ip_end = form_data.get('ipEnd')
        
        # 将IP地址转换为整数
        ip_begin_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_begin.split('.')))
        ip_end_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_end.split('.')))
        
        # 处理MAC地址
        mac_str = form_data.get('mac', '')
        if mac_str:
            mac_hex = mac_str.replace(':', '').replace(' ', '')
            mac_bytes = bytes.fromhex(mac_hex)
        else:
            mac_bytes = None
        
        # 创建新记录（移除不存在的字段）
        if 'domainName' in form_data:
            del form_data['domainName']  # 移除不存在的字段
            
        new_record = FYIP(
            ipBegin=ip_begin_int,
            ipEnd=ip_end_int,
            name=form_data.get('name'),
            group=form_data.get('group'),
            user=form_data.get('user'),
            type=form_data.get('type'),
            system=form_data.get('system'),
            mac=mac_bytes,
            manufacturer=form_data.get('manufacturer'),
            remark=form_data.get('remark'),
            port=form_data.get('port'),
            address=form_data.get('address'),
            isp=form_data.get('isp'),
            country=form_data.get('country'),
            province=form_data.get('province_name'),  # 保存名称
            city=form_data.get('city_name'),          # 保存名称
            county=form_data.get('county_name'),      # 保存名称
            longitude=float(form_data.get('longitude')) if form_data.get('longitude') else None,
            latitude=float(form_data.get('latitude')) if form_data.get('latitude') else None,
            isused=int(form_data.get('isused', 1))
        )
        
        db.session.add(new_record)
        db.session.commit()
        
        return redirect(url_for('ip.index'))
    except Exception as e:
        db.session.rollback()
        # 返回表单页面，同时返回错误信息和表单数据
        return render_template('add.html', error=str(e), form=form_data)

@ip_bp.route('/edit/<int:id>', methods=['GET'])
def edit_form(id):
    record = FYIP.query.get_or_404(id)
    return render_template('edit.html', record=record)

@ip_bp.route('/edit/<int:id>', methods=['POST'])
def edit_record(id):
    record = FYIP.query.get_or_404(id)
    try:
        # 获取表单数据
        ip_begin = request.form.get('ipBegin')
        ip_end = request.form.get('ipEnd')
        
        # 将IP地址转换为整数
        ip_begin_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_begin.split('.')))
        ip_end_int = sum(int(part) << (24-i*8) for i, part in enumerate(ip_end.split('.')))
        
        # 处理MAC地址
        mac_str = request.form.get('mac', '')
        if mac_str:
            mac_hex = mac_str.replace(':', '').replace(' ', '')
            record.mac = bytes.fromhex(mac_hex)
        else:
            record.mac = None
        
        # 更新记录
        record.ipBegin = ip_begin_int
        record.ipEnd = ip_end_int
        record.name = request.form.get('name')
        record.group = request.form.get('group')
        record.user = request.form.get('user')
        record.type = request.form.get('type')
        record.system = request.form.get('system')
        record.manufacturer = request.form.get('manufacturer')
        record.remark = request.form.get('remark')
        record.port = request.form.get('port')
        record.address = request.form.get('address')
        record.isp = request.form.get('isp')
        record.country = request.form.get('country')
        record.province = request.form.get('province')
        record.city = request.form.get('city')
        record.county = request.form.get('county')
        
        # 处理经纬度
        longitude = request.form.get('longitude', '')
        latitude = request.form.get('latitude', '')
        record.longitude = float(longitude) if longitude.strip() else None
        record.latitude = float(latitude) if latitude.strip() else None
        
        record.isused = int(request.form.get('isused', 1))
        
        db.session.commit()
        return redirect(url_for('ip.index'))
    except Exception as e:
        db.session.rollback()
        return render_template('edit.html', record=record, error=str(e))

@ip_bp.route('/delete/<int:id>', methods=['POST'])
def delete_record(id):
    record = FYIP.query.get_or_404(id)
    try:
        db.session.delete(record)
        db.session.commit()
        return jsonify({'status': 'success'})
    except:
        db.session.rollback()
        return jsonify({'status': 'error'})

@ip_bp.route('/search')
def search():
    keyword = request.args.get('keyword', '').strip()
    sel = request.args.get('sel', 'all')  # 保留原来的sel参数，用于控制搜索方式

    if not keyword:
        return render_template('search_result.html',
                             search_results=[],
                             search_type=sel)

    # 构建查询
    query = FYIP.query
    search_results = []

    if sel == 'ip':
        # 处理批量IP搜索
        ip_list = [ip.strip() for ip in keyword.split('\n') if ip.strip()]
        conditions = []
        for ip in ip_list:
            try:
                ip_int = ip_to_int(ip)
                conditions.append(
                    db.and_(FYIP.ipBegin <= ip_int, FYIP.ipEnd >= ip_int)
                )
            except:
                continue
        if conditions:
            query = query.filter(db.or_(*conditions))

        # 执行查询
        results = query.all()

        # 为每个结果找出匹配的具体IP
        found_ips = set()
        for record in results:
            matching_ips = []
            for ip in ip_list:
                try:
                    ip_int = ip_to_int(ip)
                    if record.ipBegin <= ip_int <= record.ipEnd:
                        matching_ips.append(ip)
                        found_ips.add(ip)
                except:
                    continue
            if matching_ips:
                search_results.append((record, f"匹配IP: {', '.join(matching_ips)}"))

        # 统计未查到结果的IP
        not_found_ips = [ip for ip in ip_list if ip not in found_ips]

    else:
            # 在所有字段中搜索
            conditions = []
            
            # 文本字段搜索
            text_fields = [
                FYIP.name, FYIP.group, FYIP.address, FYIP.isp,
                FYIP.user, FYIP.type, FYIP.system,
                FYIP.manufacturer, FYIP.remark,
                FYIP.country, FYIP.province, FYIP.city, FYIP.county
            ]
            
            # 特殊字段搜索
            if keyword.isdigit():
                # 端口搜索
                conditions.append(FYIP.port == int(keyword))
            
            # MAC地址搜索 - 如果输入符合MAC地址格式
            if ':' in keyword or '-' in keyword:
                try:
                    mac_parts = keyword.replace('-', ':').split(':')
                    if len(mac_parts) <= 6:  # MAC地址最多6段
                        mac_pattern = '%' + keyword.replace('-', ':') + '%'
                        conditions.append(FYIP.mac.like(mac_pattern))
                except:
                    pass
            
            # 添加文本字段的搜索条件
            for field in text_fields:
                conditions.append(field.ilike(f'%{keyword}%'))
            
            # 组合所有条件
            query = query.filter(db.or_(*conditions))
            
            # 执行查询
            results = query.all()
            
            # 为每个结果找出匹配的字段
            for record in results:
                matched_fields = []
                fields_to_check = ['name', 'group', 'address', 'isp', 'user', 'type', 
                                 'system', 'manufacturer', 'remark', 'port',
                                 'country', 'province', 'city', 'county']
                
                for field in fields_to_check:
                    if hasattr(record, field):
                        field_value = str(getattr(record, field))
                        if field_value and keyword.lower() in field_value.lower():
                            matched_fields.append(field)
                
                # 检查MAC地址
                if record.mac:
                    mac_str = format_mac(record.mac)
                    if keyword.lower() in mac_str.lower():
                        matched_fields.append('MAC地址')
                
                if matched_fields:
                    search_results.append((record, ', '.join(matched_fields)))
        
    return render_template('search_result.html',
                         search_results=search_results,
                         search_type=sel,
                         not_found_ips=not_found_ips if sel == 'ip' else None)

@ip_bp.route('/duplicate')
def duplicate():
    """查找重复记录"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    pagesize = 40
    
    # 构建子查询来找出重复的IP范围
    subquery = db.session.query(FYIP.ipBegin, FYIP.ipEnd)\
        .group_by(FYIP.ipBegin, FYIP.ipEnd)\
        .having(db.func.count('*') > 1)\
        .subquery()
    
    # 主查询获取具有重复IP范围的完整记录
    query = FYIP.query\
        .join(subquery, db.and_(
            FYIP.ipBegin == subquery.c.ipBegin,
            FYIP.ipEnd == subquery.c.ipEnd
        ))\
        .order_by(FYIP.ipBegin)
    
    # 获取总记录数
    recordcount = query.count()
    
    # 计算总页数
    pagecount = (recordcount + pagesize - 1) // pagesize
    
    # 获取当前页的记录
    records = query.offset((page - 1) * pagesize).limit(pagesize).all()
    
    return render_template('duplicate.html',
                         records=records,
                         page=page,
                         pagecount=pagecount,
                         recordcount=recordcount)

@ip_bp.route('/find_duplicates')
def find_duplicates():
    """查找并删除重复记录"""
    # 查找完全相同的IP范围记录
    duplicates = db.session.query(
        FYIP.ipBegin,
        FYIP.ipEnd,
        db.func.count('*').label('count')
    ).group_by(
        FYIP.ipBegin,
        FYIP.ipEnd
    ).having(
        db.func.count('*') > 1
    ).all()
    
    return render_template('find_duplicates.html', 
                         duplicates=duplicates,
                         get_duplicate_records=get_duplicate_records)

def get_duplicate_records(ip_begin, ip_end):
    """获取指定IP范围的所有重复记录"""
    return FYIP.query.filter(
        FYIP.ipBegin == ip_begin,
        FYIP.ipEnd == ip_end
    ).all()

@ip_bp.route('/delete_duplicates', methods=['POST'])
def delete_duplicates():
    """合并重复记录，将相同字段合并，不同字段用逗号分隔保留"""
    try:
        ip_begin = request.form.get('ipBegin', type=int)
        ip_end = request.form.get('ipEnd', type=int)
        
        if not ip_begin or not ip_end:
            return jsonify({'status': 'error', 'message': '参数错误'})
        
        # 查找指定IP范围的所有记录
        records = FYIP.query.filter(
            FYIP.ipBegin == ip_begin,
            FYIP.ipEnd == ip_end
        ).order_by(
            FYIP.id  # 按ID排序，保留最早的记录作为基础
        ).all()
        
        if len(records) <= 1:
            return jsonify({'status': 'error', 'message': '没有重复记录'})
        
        # 使用第一条记录作为基础
        base_record = records[0]
        
        # 用于存储每个字段的唯一值
        field_values = {
            'name': set(),
            'address': set(),
            'group': set(),
            'user': set(),
            'type': set(),
            'system': set(),
            'manufacturer': set(),
            'remark': set(),
            'port': set(),
            'isp': set(),
            'country': set(),
            'province': set(),
            'city': set(),
            'county': set()  # 移除street字段
        }
        
        # 收集所有记录的字段值
        for record in records:
            for field in field_values:
                value = getattr(record, field)
                if value:  # 只收集非空值
                    field_values[field].add(value)
        
        # 更新基础记录的字段
        for field, values in field_values.items():
            if values:  # 如果有收集到的值
                merged_value = ','.join(sorted(values))  # 按字母顺序排序，保证结果一致性
                setattr(base_record, field, merged_value)
        
        # 删除其他记录
        for record in records[1:]:
            db.session.delete(record)
        
        db.session.commit()
        return jsonify({'status': 'success'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/merge_records')
def merge_records():
    """合并IP范围相同的记录"""
    # 查找IP范围相同的记录组
    merge_groups = db.session.query(
        FYIP.ipBegin,
        FYIP.ipEnd,
        db.func.count(FYIP.id).label('count'),
        db.func.group_concat(FYIP.id).label('ids'),
        db.func.group_concat(db.func.nullif(FYIP.name, '')).label('names'),
        db.func.group_concat(db.func.nullif(FYIP.address, '')).label('addresses'),
        db.func.group_concat(db.func.nullif(FYIP.group, '')).label('groups'),
        db.func.group_concat(db.func.nullif(FYIP.isp, '')).label('isps')
    ).group_by(
        FYIP.ipBegin,
        FYIP.ipEnd
    ).having(
        db.func.count(FYIP.id) > 1
    ).order_by(
        FYIP.ipBegin,
        FYIP.ipEnd
    ).all()
    
    return render_template('merge_records.html',
                         merge_groups=merge_groups,
                         get_detail_records=get_detail_records)

def get_detail_records(ids):
    """获取指定ID列表的详细记录"""
    if isinstance(ids, str):
        id_list = [int(id) for id in ids.split(',')]
    else:
        id_list = ids
    return FYIP.query.filter(FYIP.id.in_(id_list)).order_by(FYIP.id).all()

@ip_bp.route('/do_merge', methods=['POST'])
def do_merge():
    """执行记录合并操作"""
    try:
        # 查找所有需要合并的组
        merge_groups = db.session.query(
            FYIP.ipBegin,
            FYIP.ipEnd,
            db.func.group_concat(FYIP.id).label('ids'),
            db.func.group_concat(db.func.nullif(FYIP.name, '')).label('names'),
            db.func.group_concat(db.func.nullif(FYIP.address, '')).label('addresses'),
            db.func.group_concat(db.func.nullif(FYIP.group, '')).label('groups'),
            db.func.group_concat(db.func.nullif(FYIP.isp, '')).label('isps')
        ).group_by(
            FYIP.ipBegin,
            FYIP.ipEnd
        ).having(
            db.func.count(FYIP.id) > 1
        ).all()
        
        for group in merge_groups:
            # 获取该组所有记录
            records = get_detail_records(group.ids)
            if len(records) < 2:
                continue
                
            # 使用第一条记录作为基础
            base_record = records[0]
            
            # 更新合并后的信息
            base_record.name = group.names
            base_record.address = group.addresses
            base_record.group = group.groups
            base_record.isp = group.isps
            
            # 删除其他记录
            for record in records[1:]:
                db.session.delete(record)
        
        db.session.commit()
        return jsonify({'status': 'success'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/ip_stats', methods=['GET', 'POST'])
def ip_stats():
    """IP分组实际网段范围统计"""
    # 获取所有ISP
    isps = db.session.query(FYIP.isp).distinct().order_by(FYIP.isp).all()
    
    # 获取选中的ISP
    selected_isps = request.form.getlist('isps[]') if request.method == 'POST' else []
    
    # 构建基础查询
    base_query = db.session.query(FYIP.group).distinct()
    if selected_isps:
        base_query = base_query.filter(FYIP.isp.in_(selected_isps))
    groups = base_query.order_by(FYIP.group).all()
    
    group_stats = []
    for group in groups:
        group_name = group[0]
        if not group_name:
            continue
            
        # 获取该组的所有IP范围
        ranges_query = db.session.query(FYIP.ipBegin, FYIP.ipEnd)\
            .filter(FYIP.group == group_name)
        if selected_isps:
            ranges_query = ranges_query.filter(FYIP.isp.in_(selected_isps))
        ranges = ranges_query.order_by(FYIP.ipBegin).all()
        
        if not ranges:
            continue
            
        # 合并IP范围
        merged_networks = []
        current = None
        
        for range_item in ranges:
            if current is None:
                current = {
                    'start': range_item.ipBegin,
                    'end': range_item.ipEnd
                }
            else:
                if range_item.ipBegin <= current['end'] + 1:
                    # 合并重叠范围
                    current['end'] = max(current['end'], range_item.ipEnd)
                else:
                    # 计算当前范围的最优CIDR
                    cidr = calculate_optimal_cidr(current['start'], current['end'])
                    merged_networks.append({
                        'start_ip': int_to_ip(current['start']),
                        'end_ip': int_to_ip(current['end']),
                        'cidr': f"{int_to_ip(cidr['network'])}/{cidr['bits']}",
                        'ip_count': current['end'] - current['start'] + 1
                    })
                    current = {
                        'start': range_item.ipBegin,
                        'end': range_item.ipEnd
                    }
        
        # 处理最后一个范围
        if current:
            cidr = calculate_optimal_cidr(current['start'], current['end'])
            merged_networks.append({
                'start_ip': int_to_ip(current['start']),
                'end_ip': int_to_ip(current['end']),
                'cidr': f"{int_to_ip(cidr['network'])}/{cidr['bits']}",
                'ip_count': current['end'] - current['start'] + 1
            })
        
        # 计算总IP数量
        total_ips = sum(network['ip_count'] for network in merged_networks)
        
        group_stats.append({
            'name': group_name,
            'network_count': len(merged_networks),
            'total_ips': total_ips,
            'networks': merged_networks
        })
    
    return render_template('ip_stats.html',
                         isps=isps,
                         selected_isps=selected_isps,
                         group_stats=group_stats)

def calculate_optimal_cidr(start_ip, end_ip):
    """计算最优CIDR表示"""
    # 如果开始IP和结束IP相同，返回/32
    if start_ip == end_ip:
        return {
            'network': start_ip,
            'bits': 32
        }
    
    # 计算需要的比特数
    diff = end_ip - start_ip + 1
    bits = 32 - (diff - 1).bit_length()
    
    # 创建掩码
    mask = (0xFFFFFFFF << (32 - bits)) & 0xFFFFFFFF  # 确保是32位无符号整数
    
    # 计算网络地址
    network = start_ip & mask
    broadcast = network | (~mask & 0xFFFFFFFF)
    
    # 检查是否是有效的CIDR块
    if network <= start_ip and broadcast >= end_ip:
        return {
            'network': network,
            'bits': bits
        }
    
    # 如果不能完美匹配，则使用下一个更小的掩码
    bits -= 1
    mask = (0xFFFFFFFF << (32 - bits)) & 0xFFFFFFFF  # 确保是32位无符号整数
    return {
        'network': start_ip & mask,
        'bits': bits
    }

@ip_bp.app_template_filter('number_format')
def number_format(value):
    """格式化数字，添加千位分隔符"""
    return "{:,}".format(value)



@ip_bp.route('/merge_ip_ranges', methods=['GET', 'POST'])
def merge_ip_ranges():
    """IP段合并工具"""
    if request.method == 'POST' and request.form.get('ip_ranges'):
        ip_ranges = request.form.get('ip_ranges').strip()
        if not ip_ranges:
            return render_template('merge_ip_ranges.html')
            
        lines = ip_ranges.split('\n')
        errors = []
        ranges = []
        
        # 验证并解析每一行
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            # 验证格式
            validation_result = validate_ip_format(line, i)
            if validation_result is not True:
                errors.append(validation_result)
                continue
                
            # 解析IP范围
            ip_range = parse_ip_range(line)
            if ip_range:
                ranges.append(ip_range)
        
        if errors:
            return render_template('merge_ip_ranges.html', errors=errors)
            
        # 排序并合并重叠的范围
        ranges.sort(key=lambda x: x[0])
        merged = merge_ranges(ranges)
        
        # 生成不同格式的结果
        result = {
            'cidrs': generate_cidrs(merged),
            'subnets': generate_subnets(merged),
            'ranges': generate_ranges(merged)
        }
        
        return render_template('merge_ip_ranges.html', merged_ranges=result)
        
    return render_template('merge_ip_ranges.html')

def validate_ip_format(line, line_number):
    """验证IP格式"""
    # 子网掩码格式: *********** *************
    if ' ' in line:
        ip, mask = line.split(' ', 1)
        if not is_valid_ip(ip) or not is_valid_mask(mask):
            return f"第 {line_number} 行格式错误: {line}"
        return True
        
    # CIDR格式: ***********/24
    if '/' in line:
        ip, bits = line.split('/', 1)
        if not is_valid_ip(ip) or not bits.isdigit() or not 0 <= int(bits) <= 32:
            return f"第 {line_number} 行格式错误: {line}"
        return True
        
    # 范围格式: ***********-*************
    if '-' in line:
        start, end = line.split('-', 1)
        if '.' not in end:  # 简写格式
            start_parts = start.split('.')
            if len(start_parts) != 4:
                return f"第 {line_number} 行格式错误: {line}"
            end = '.'.join(start_parts[:-1] + [end])
        if not is_valid_ip(start) or not is_valid_ip(end):
            return f"第 {line_number} 行格式错误: {line}"
        if ip_to_int(start) > ip_to_int(end):
            return f"第 {line_number} 行IP范围起始地址大于结束地址: {line}"
        return True
        
    # 单个IP
    if not is_valid_ip(line):
        return f"第 {line_number} 行格式错误: {line}"
    return True

def is_valid_ip(ip):
    """验证IP地址格式"""
    try:
        parts = ip.split('.')
        if len(parts) != 4:
            return False
        return all(part.isdigit() and 0 <= int(part) <= 255 for part in parts)
    except:
        return False

def is_valid_mask(mask):
    """验证子网掩码格式"""
    if not is_valid_ip(mask):
        return False
    # 验证掩码是否有效（必须是连续的1后跟连续的0）
    mask_int = ip_to_int(mask)
    found_zero = False
    for i in range(31, -1, -1):
        bit = (mask_int >> i) & 1
        if found_zero and bit == 1:
            return False
        if bit == 0:
            found_zero = True
    return True

def parse_ip_range(line):
    """解析IP范围"""
    line = line.strip()
    if not line:
        return None
        
    # 子网掩码格式
    if ' ' in line:
        ip, mask = line.split(' ', 1)
        ip_int = ip_to_int(ip)
        mask_int = ip_to_int(mask)
        network = ip_int & mask_int
        broadcast = network | (~mask_int & 0xFFFFFFFF)
        return (network, broadcast)
        
    # CIDR格式
    if '/' in line:
        ip, bits = line.split('/', 1)
        ip_int = ip_to_int(ip)
        bits = int(bits)
        mask = 0xFFFFFFFF << (32 - bits)
        network = ip_int & mask
        broadcast = network | (~mask & 0xFFFFFFFF)
        return (network, broadcast)
        
    # 范围格式
    if '-' in line:
        start, end = line.split('-', 1)
        if '.' not in end:  # 简写格式
            start_parts = start.split('.')
            end = '.'.join(start_parts[:-1] + [end])
        return (ip_to_int(start), ip_to_int(end))
        
    # 单个IP
    return (ip_to_int(line), ip_to_int(line))

def merge_ranges(ranges):
    """合并重叠的IP范围"""
    if not ranges:
        return []
        
    merged = []
    current = ranges[0]
    
    for range_item in ranges[1:]:
        if range_item[0] <= current[1] + 1:
            # 合并重叠范围
            current = (current[0], max(current[1], range_item[1]))
        else:
            # 处理当前范围
            merged.append(current)
            current = range_item
            
    merged.append(current)
    return merged

def generate_cidrs(ranges):
    """生成CIDR格式结果"""
    cidrs = []
    for start, end in ranges:
        # 使用改进的CIDR计算
        sub_cidrs = split_to_cidrs(start, end)
        for cidr in sub_cidrs:
            network = int_to_ip(cidr['network'])
            ip_count = 2 ** (32 - cidr['bits'])
            cidrs.append({
                'network': network,
                'bits': cidr['bits'],
                'ip_count': ip_count
            })
    return cidrs

def generate_subnets(ranges):
    """生成子网掩码格式结果"""
    subnets = []
    for start, end in ranges:
        sub_cidrs = split_to_cidrs(start, end)
        for cidr in sub_cidrs:
            network = int_to_ip(cidr['network'])
            # 修复掩码计算
            mask_int = (0xFFFFFFFF << (32 - cidr['bits'])) & 0xFFFFFFFF  # 确保是32位无符号整数
            mask = int_to_ip(mask_int)
            ip_count = 2 ** (32 - cidr['bits'])
            subnets.append({
                'network': network,
                'mask': mask,
                'ip_count': ip_count
            })
    return subnets

def generate_ranges(ranges):
    """生成IP范围格式结果"""
    return [{
        'start': int_to_ip(start),
        'end': int_to_ip(end),
        'ip_count': end - start + 1
    } for start, end in ranges]

def split_to_cidrs(start, end):
    """将IP范围分割为CIDR块"""
    cidrs = []
    while start <= end:
        # 从最大的掩码开始尝试(/32)，找到能包含当前起始IP的最大CIDR块
        max_size = 0
        best_prefix = 32
        best_network = start
        
        # 尝试从/32到/0的每个掩码
        for prefix in range(32, -1, -1):
            mask = (0xFFFFFFFF << (32 - prefix)) & 0xFFFFFFFF  # 确保是32位无符号整数
            network = start & mask
            broadcast = network | (~mask & 0xFFFFFFFF)
            
            # 检查这个CIDR块是否完全在范围内
            if network >= start and broadcast <= end:
                size = broadcast - network + 1
                if size > max_size:
                    max_size = size
                    best_prefix = prefix
                    best_network = network
        
        # 添加找到的最佳CIDR块
        cidrs.append({
            'network': best_network,
            'bits': best_prefix
        })
        
        # 移动到下一个未覆盖的IP
        mask = (0xFFFFFFFF << (32 - best_prefix)) & 0xFFFFFFFF  # 确保是32位无符号整数
        broadcast = best_network | (~mask & 0xFFFFFFFF)
        start = broadcast + 1
        
    return cidrs

@ip_bp.route('/group_by_region', methods=['GET', 'POST'])
def group_by_region():
    # 获取所有ISP
    isps = db.session.query(FYIP.isp).distinct()
    isps = [{'isp': isp[0] if isp[0] else '未知ISP'} for isp in isps]
    
    # 获取选中的ISP
    selected_isps = request.form.getlist('isps[]') if request.method == 'POST' else []
    
    # 获取所有国家
    countries = [c[0] for c in db.session.query(FYIP.country).distinct()
                                       .filter(FYIP.country.isnot(None))
                                       .filter(FYIP.country != '')
                                       .order_by(FYIP.country)]
    
    if request.method == 'POST' and 'do_group' in request.form:
        country = request.form.get('country')
        province = request.form.get('province')
        city = request.form.get('city')
        county = request.form.get('county')
        
        # 构建查询条件
        conditions = []
        if country: conditions.append(FYIP.country == country)
        if province: conditions.append(FYIP.province == province)
        if city: conditions.append(FYIP.city == city)
        if county: conditions.append(FYIP.county == county)
    
    # 添加ISP过滤条件
    if selected_isps:
        isp_conditions = []
        for isp in selected_isps:
            if isp == '未知ISP':
                    isp_conditions.append(db.or_(FYIP.isp.is_(None), FYIP.isp == ''))
            else:
                isp_conditions.append(FYIP.isp == isp)
            conditions.append(db.or_(*isp_conditions))
        
        if conditions:
            # 获取符合条件的IP段
            query = db.session.query(FYIP.ipBegin, FYIP.ipEnd).filter(*conditions)
            networks = [{'start': row[0], 'end': row[1]} for row in query]
            
            if networks:
                # 合并重叠的网段并计算CIDR
                merged_networks = merge_ip_ranges(networks)
                total_ips = sum(net['ip_count'] for net in merged_networks)
                
                return render_template('group_by_region.html',
                    isps=isps,
                    selected_isps=selected_isps,
                    countries=countries,
                    selected_country=country,
                    selected_province=province,
                    selected_city=city,
                    selected_county=county,
                    merged_networks=merged_networks,
                    total_ips=total_ips
                )
            else:
                error = "未找到匹配的记录"
        else:
            error = "请至少选择一个地区条件"
            
        return render_template('group_by_region.html',
            isps=isps,
            selected_isps=selected_isps,
            countries=countries,
            error=error
        )
    
    return render_template('group_by_region.html',
        isps=isps,
        selected_isps=selected_isps,
        countries=countries
    )

def merge_ip_ranges(networks):
    """合并重叠的IP段并计算CIDR"""
    if not networks:
        return []
    
    # 按起始IP排序
    networks.sort(key=lambda x: x['start'])
    
    merged = []
    current = networks[0].copy()
    
    for network in networks[1:]:
        if current['end'] + 1 >= network['start']:
            # 合并重叠的网段
            current['end'] = max(current['end'], network['end'])
        else:
            # 处理当前网段
            process_network(current)
            merged.append(current)
            current = network.copy()
    
    # 处理最后一个网段
    process_network(current)
    merged.append(current)
    
    return merged

def process_network(network):
    """处理单个网段，添加IP地址显示和CIDR计算"""
    network['start_ip'] = socket.inet_ntoa(struct.pack('!L', network['start']))
    network['end_ip'] = socket.inet_ntoa(struct.pack('!L', network['end']))
    network['ip_count'] = network['end'] - network['start'] + 1
    network['cidrs'] = calculate_optimal_cidrs(network['start'], network['end'])

def calculate_optimal_cidrs(start_ip, end_ip):
    """计算最优CIDR表示"""
    cidrs = []
    while start_ip <= end_ip:
        max_size = 0
        best_prefix = 32
        
        for prefix in range(32, -1, -1):
            mask = 0xffffffff << (32 - prefix)
            network = start_ip & mask
            broadcast = network | (~mask & 0xffffffff)
            
            if network == start_ip and broadcast <= end_ip:
                size = 1 << (32 - prefix)
                if size > max_size:
                    max_size = size
                    best_prefix = prefix
        
        if max_size > 0:
            mask = 0xffffffff << (32 - best_prefix)
            network = start_ip & mask
            cidrs.append({
                'network': socket.inet_ntoa(struct.pack('!L', network)),
                'prefix': best_prefix
            })
            start_ip = network + max_size
        else:
            start_ip += 1
            
    return cidrs

@ip_bp.route('/get_regions')
def get_regions():
    try:
        type = request.args.get('type')
        if not type:
            return jsonify({'error': 'type 参数是必需的'}), 400
            
        if type not in ['province', 'city', 'county']:
            return jsonify({'error': f'无效的 type 参数: {type}'}), 400
            
        parent = request.args.get('parent')
        
        query = db.session.query(getattr(FYIP, type)).distinct()
        
        # 添加父级过滤条件
        if parent:
            parent_field = {
                'province': FYIP.country,
                'city': FYIP.province,
                'county': FYIP.city
            }.get(type)
            
            if parent_field:
                query = query.filter(parent_field == parent)
        
        # 过滤空值并排序
        regions = [r[0] for r in query.filter(getattr(FYIP, type).isnot(None))
                              .filter(getattr(FYIP, type) != '')
                              .order_by(getattr(FYIP, type))]
                              
        # 确保返回的是有效的JSON数据
        return jsonify({'data': regions, 'status': 'success'})
        
    except AttributeError as e:
        print(f'数据库字段错误: {str(e)}')
        return jsonify({'error': '数据库字段错误', 'status': 'error'}), 500
    except Exception as e:
        print(f'get_regions 发生错误: {str(e)}')
        return jsonify({'error': '服务器内部错误', 'status': 'error'}), 500



@ip_bp.route('/check_groups')
def check_groups():
    """分组信息检查与修改"""
    # 获取所有不同的分组
    groups = db.session.query(FYIP.group).distinct().all()
    return render_template('check_groups.html', groups=groups)

@ip_bp.route('/check_region', methods=['GET', 'POST'])
def check_region():
    try:
        if request.method == 'POST':
            action = request.form.get('action')
            if action == 'batch_update':
                try:
                    # 获取要更新的字段和记录ID
                    fields = request.form.getlist('fields[]')
                    rows = request.form.getlist('rows[]')
                    
                    print("接收到的表单数据:")
                    for key in request.form:
                        print(f"{key}: {request.form.getlist(key)}")
                    
                    if not fields or not rows:
                        return jsonify({
                            'status': 'error',
                            'message': '未选择要更新的字段或记录'
                        })
                    
                    # 更新记录
                    updated_count = 0
                    for row_id in rows:
                        record = FYIP.query.get(row_id)
                        if record:
                            print(f"\n更新记录 {row_id}:")
                            for field in fields:
                                if hasattr(record, field):
                                    old_value = getattr(record, field)
                                    value = request.form.get(field, '')
                                    setattr(record, field, value)
                                    print(f"  {field}: {old_value} -> {value}")
                            updated_count += 1
                    
                    db.session.commit()
                    return jsonify({
                        'status': 'success',
                        'message': f'成功更新 {updated_count} 条记录'
                    })
                    
                except Exception as e:
                    db.session.rollback()
                    print(f"批量更新错误: {str(e)}")
                    return jsonify({
                        'status': 'error',
                        'message': f'更新失败: {str(e)}'
                    })
            elif action == 'batch_delete':
                try:
                    # 获取要删除的记录ID
                    rows = request.form.getlist('rows[]')
                    
                    print("接收到的批量删除请求:")
                    print(f"要删除的记录ID: {rows}")
                    
                    if not rows:
                        return jsonify({
                            'status': 'error',
                            'message': '未选择要删除的记录'
                        })
                    
                    # 删除记录
                    deleted_count = 0
                    for row_id in rows:
                        record = FYIP.query.get(row_id)
                        if record:
                            db.session.delete(record)
                            deleted_count += 1
                    
                    db.session.commit()
                    return jsonify({
                        'status': 'success',
                        'message': f'成功删除 {deleted_count} 条记录'
                    })
                    
                except Exception as e:
                    db.session.rollback()
                    print(f"批量删除错误: {str(e)}")
                    return jsonify({
                        'status': 'error',
                        'message': f'删除失败: {str(e)}'
                    })
            else:
                # 处理搜索请求
                page = request.form.get('page', 1, type=int)
                per_page = 10
                
                # 获取所有数据
                query = FYIP.query

                # 关键字搜索
                keyword = request.form.get('keyword', '').strip()
                if keyword:
                    keyword_filter = db.or_(
                        FYIP.name.ilike(f'%{keyword}%'),
                        FYIP.group.ilike(f'%{keyword}%'),
                        FYIP.user.ilike(f'%{keyword}%'),
                        FYIP.type.ilike(f'%{keyword}%'),
                        FYIP.system.ilike(f'%{keyword}%'),
                        FYIP.manufacturer.ilike(f'%{keyword}%'),
                        FYIP.remark.ilike(f'%{keyword}%'),
                        FYIP.port.ilike(f'%{keyword}%'),
                        FYIP.country.ilike(f'%{keyword}%'),
                        FYIP.province.ilike(f'%{keyword}%'),
                        FYIP.city.ilike(f'%{keyword}%'),
                        FYIP.county.ilike(f'%{keyword}%'),
                        FYIP.address.ilike(f'%{keyword}%'),
                        FYIP.isp.ilike(f'%{keyword}%')
                    )
                    query = query.filter(keyword_filter)

                # IP网段搜索
                ip_range = request.form.get('ip_range_search', '').strip()
                if ip_range:
                    try:
                        if '/' in ip_range:  # CIDR格式
                            network = ipaddress.ip_network(ip_range, strict=False)
                            start_ip = int(network[0])
                            end_ip = int(network[-1])
                            # 修改查询条件，确保IP范围完全在搜索网段内
                            query = query.filter(
                                db.and_(
                                    FYIP.ipBegin >= start_ip,
                                    FYIP.ipEnd <= end_ip
                                )
                            )
                        elif '-' in ip_range:  # IP范围格式
                            start_search, end_search = ip_range.split('-')
                            start_ip = int(ipaddress.IPv4Address(start_search.strip()))
                            end_ip = int(ipaddress.IPv4Address(end_search.strip()))
                            # 修改查询条件，确保IP范围完全在搜索范围内
                            query = query.filter(
                                db.and_(
                                    FYIP.ipBegin >= start_ip,
                                    FYIP.ipEnd <= end_ip
                                )
                            )
                        else:  # 单个IP
                            search_ip = int(ipaddress.IPv4Address(ip_range))
                            # 修改查询条件，查找包含这个IP的范围
                            query = query.filter(
                                db.and_(
                                    FYIP.ipBegin <= search_ip,
                                    FYIP.ipEnd >= search_ip
                                )
                            )
                    except Exception as e:
                        print(f"IP搜索错误: {e}")
                        return jsonify({
                            'status': 'error',
                            'message': f'IP格式错误: {str(e)}'
                        })

                # 其他搜索条件
                for field in ['name', 'user', 'group', 'type', 'system', 'remark', 'country', 
                            'province', 'city', 'county', 'address', 'isp']:
                    value = request.form.get(field, '').strip()
                    if value:
                        query = query.filter(getattr(FYIP, field).ilike(f'%{value}%'))

                # 分页
                pagination = query.paginate(
                    page=page,
                    per_page=per_page,
                    error_out=False
                )

                # 转换结果为字典列表
                results = []
                for item in pagination.items:
                    result = item.to_dict()  # 确保你的模型有这个方法
                    results.append(result)

                return jsonify({
                    'data': results,
                    'total': pagination.total,
                    'current_page': page
                })

        # GET请求处理
        page = request.args.get('page', 1, type=int)
        keyword_search = request.args.get('keyword_search', '')
        ip_range_search = request.args.get('ip_range_search', '')
        
        query = FYIP.query
        display_format = None  # 用于显示的IP范围格式
        
        # 关键字搜索
        if keyword_search:
            keyword_filter = db.or_(
                FYIP.name.ilike(f'%{keyword_search}%'),
                FYIP.group.ilike(f'%{keyword_search}%'),
                FYIP.user.ilike(f'%{keyword_search}%'),
                FYIP.type.ilike(f'%{keyword_search}%'),
                FYIP.system.ilike(f'%{keyword_search}%'),
                FYIP.manufacturer.ilike(f'%{keyword_search}%'),
                FYIP.remark.ilike(f'%{keyword_search}%'),
                FYIP.port.ilike(f'%{keyword_search}%'),
                FYIP.country.ilike(f'%{keyword_search}%'),
                FYIP.province.ilike(f'%{keyword_search}%'),
                FYIP.city.ilike(f'%{keyword_search}%'),
                FYIP.county.ilike(f'%{keyword_search}%'),
                FYIP.address.ilike(f'%{keyword_search}%'),
                FYIP.isp.ilike(f'%{keyword_search}%')
            )
            query = query.filter(keyword_filter)
            
        # 如果有IP网段搜索
        if ip_range_search:
            try:
                # 处理IP范围搜索
                if '-' in ip_range_search:
                    # 处理IP范围格式 (例如: ***********-*************)
                    start_ip, end_ip = ip_range_search.split('-')
                    start_ip = ip_to_int(start_ip.strip())
                    end_ip = ip_to_int(end_ip.strip())
                    display_format = ip_range_search
                else:
                    # 处理CIDR格式 (例如: ***********/24) 或单个IP
                    if '/' in ip_range_search:
                        # CIDR格式
                        network = ipaddress.ip_network(ip_range_search.strip(), strict=False)
                        start_ip = int(network[0])
                        end_ip = int(network[-1])
                        display_format = f"{network[0]}-{network[-1]}"
                    else:
                        # 单个IP
                        ip = ip_to_int(ip_range_search.strip())
                        start_ip = end_ip = ip
                        display_format = ip_range_search

                # 添加调试日志
                print(f"搜索IP范围: start_ip={start_ip}, end_ip={end_ip}")
                
                # 修改查询条件，确保IP范围完全在搜索范围内
                query = query.filter(
                    db.and_(
                        FYIP.ipBegin >= start_ip,
                        FYIP.ipEnd <= end_ip
                    )
                )
                
            except ValueError as e:
                print(f"IP转换错误: {str(e)}")
                flash(f'IP格式错误: {str(e)}', 'error')
                return redirect(url_for('ip.check_region'))
            except Exception as e:
                print(f"IP范围处理错误: {str(e)}")
                flash(f'IP范围处理错误: {str(e)}', 'error')
                return redirect(url_for('ip.check_region'))

        # 其他搜索条件
        for field in ['country', 'province', 'city', 'county']:
            value = request.args.get(field, '').strip()
            if value:
                query = query.filter(getattr(FYIP, field).ilike(f'%{value}%'))

        # 分页
        per_page = 50
        try:
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )
            
            # 添加调试日志
            print(f"查询结果数: {pagination.total}")
            
            return render_template('check_region.html',
                                records=pagination.items,
                                pagination=pagination,
                                keyword_search=keyword_search,    # 关键字搜索
                                ip_range_search=ip_range_search,  # 原始输入格式，用于搜索框
                                display_format=display_format,    # 转换后的范围格式，用于显示
                                country=request.args.get('country', ''),
                                province=request.args.get('province', ''),
                                city=request.args.get('city', ''),
                                county=request.args.get('county', ''))
                                
        except Exception as e:
            print(f"分页查询错误: {str(e)}")
            db.session.rollback()
            flash('查询出错，请重试', 'error')
            return redirect(url_for('ip.check_region'))
            
    except Exception as e:
        print(f"check_region整体错误: {str(e)}")
        db.session.rollback()
        flash('系统错误，请重试', 'error')
        return redirect(url_for('ip.check_region'))

@ip_bp.route('/update_field', methods=['POST'])
def update_field():
    """更新单个字段"""
    try:
        record_id = request.form.get('id')
        field = request.form.get('field')
        value = request.form.get('value')
        
        record = FYIP.query.get_or_404(record_id)
        
        # 特殊字段处理
        if field in ['ipBegin', 'ipEnd']:
            value = ip_to_int(value)
        elif field == 'mac':
            if value:
                value = bytes.fromhex(value.replace(':', '').replace('-', ''))
            else:
                value = None
        elif field in ['longitude', 'latitude']:
            value = float(value) if value else None
        elif field == 'isused':
            value = int(value)
        
        setattr(record, field, value)
        db.session.commit()
        
        return jsonify({'status': 'success'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/traceroute/<int:ip_int>/<int:fyip_id>', methods=['POST'])
def do_traceroute(ip_int, fyip_id):
    """执行ICMP traceroute并保存结果"""
    if not SCAPY_AVAILABLE:
        return jsonify({'status': 'error', 'message': 'Scapy库不可用，无法执行traceroute功能'})

    try:
        # 删除同一目标IP和fyip_id的ICMP跟踪记录
        db.session.execute(
            """DELETE FROM traceroute_records 
               WHERE target_ip = :target_ip 
               AND fyip_id = :fyip_id
               AND trace_type = 'ICMP'""",  # 只删除ICMP类型的记录
            {'target_ip': ip_int, 'fyip_id': fyip_id}
        )
        db.session.commit()

        # 将整数IP转换为点分十进制格式
        target_ip = int_to_ip(ip_int)
        max_hops = 10  # 最大跳数限制为10跳
        results = []
        
        # 记录跟踪开始时间
        trace_time = datetime.now()

        for ttl in range(1, max_hops + 1):
            # 构造和发送数据包
            pkt = IP(dst=target_ip, ttl=ttl) / ICMP()
            start_time = time.time()
            reply = sr1(pkt, timeout=2, verbose=0)
            rtt = (time.time() - start_time) * 1000  # 转换为毫秒

            if reply is None:
                # 超时的情况，记录为*
                trace_record = {
                    'target_ip': ip_int,
                    'hop_number': ttl,
                    'hop_ip': 0,  # 用0表示超时
                    'rtt_ms': 0.0,  # 超时时RTT记为0
                    'trace_time': trace_time,  # 每一跳都记录相同的时间
                    'fyip_id': fyip_id,  # 添加fyip_id
                    'trace_type': 'ICMP'  # 添加跟踪类型
                }
                
                # 插入记录
                try:
                    db.session.execute(
                        """INSERT INTO traceroute_records 
                           (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                           VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                        trace_record
                    )
                    db.session.commit()
                    results.append({
                        'hop': ttl,
                        'ip': '*',  # 显示为星号
                        'rtt': 0
                    })
                except Exception as e:
                    db.session.rollback()
                    return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})
                
                continue

            # 有响应的情况
            hop_ip = ip_to_int(reply.src)
            
            # 保存到数据库
            trace_record = {
                'target_ip': ip_int,
                'hop_number': ttl,
                'hop_ip': hop_ip,
                'rtt_ms': rtt,
                'trace_time': trace_time,
                'fyip_id': fyip_id,
                'trace_type': 'ICMP'  # 添加跟踪类型
            }
            
            try:
                db.session.execute(
                    """INSERT INTO traceroute_records 
                       (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                       VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                    trace_record
                )
                db.session.commit()
                results.append({
                    'hop': ttl,
                    'ip': reply.src,
                    'rtt': round(rtt, 2)
                })
            except Exception as e:
                db.session.rollback()
                return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})

            # 如果到达目标IP，结束跟踪
            if reply.src == target_ip:
                break

        return jsonify({
            'status': 'success',
            'message': '跟踪完成',
            'results': results,
            'target_ip': target_ip,  # 添加目标IP
            'trace_time': trace_time.strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/udp_traceroute/<int:ip_int>/<int:fyip_id>', methods=['POST'])
def do_udp_traceroute(ip_int, fyip_id):
    """执行UDP traceroute并保存结果"""
    if not SCAPY_AVAILABLE:
        return jsonify({'status': 'error', 'message': 'Scapy库不可用，无法执行UDP traceroute功能'})

    try:
        # 删除同一目标IP和fyip_id的UDP跟踪记录
        db.session.execute(
            """DELETE FROM traceroute_records 
               WHERE target_ip = :target_ip 
               AND fyip_id = :fyip_id
               AND trace_type = 'UDP'""",  # 只删除UDP类型的记录
            {'target_ip': ip_int, 'fyip_id': fyip_id}
        )
        db.session.commit()

        # 将整数IP转换为点分十进制格式
        target_ip = int_to_ip(ip_int)
        max_hops = 10  # 最大跳数限制为10跳
        results = []
        
        # 记录跟踪开始时间
        trace_time = datetime.now()

        for ttl in range(1, max_hops + 1):
            # 构造和发送UDP数据包
            pkt = IP(dst=target_ip, ttl=ttl) / UDP(dport=33434)
            start_time = time.time()
            reply = sr1(pkt, timeout=2, verbose=0)
            rtt = (time.time() - start_time) * 1000  # 转换为毫秒

            if reply is None:
                # 超时的情况，记录为*
                trace_record = {
                    'target_ip': ip_int,
                    'hop_number': ttl,
                    'hop_ip': 0,  # 用0表示超时
                    'rtt_ms': 0.0,  # 超时时RTT记为0
                    'trace_time': trace_time,  # 每一跳都记录相同的时间
                    'fyip_id': fyip_id,  # 添加fyip_id
                    'trace_type': 'UDP'  # 添加跟踪类型
                }
                
                # 插入记录
                try:
                    db.session.execute(
                        """INSERT INTO traceroute_records 
                           (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                           VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                        trace_record
                    )
                    db.session.commit()
                    results.append({
                        'hop': ttl,
                        'ip': '*',  # 显示为星号
                        'rtt': 0
                    })
                except Exception as e:
                    db.session.rollback()
                    return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})
                
                continue

            # 有响应的情况
            hop_ip = ip_to_int(reply.src)
            
            # 保存到数据库
            trace_record = {
                'target_ip': ip_int,
                'hop_number': ttl,
                'hop_ip': hop_ip,
                'rtt_ms': rtt,
                'trace_time': trace_time,
                'fyip_id': fyip_id,
                'trace_type': 'UDP'  # 添加跟踪类型
            }
            
            try:
                db.session.execute(
                    """INSERT INTO traceroute_records 
                       (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                       VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                    trace_record
                )
                db.session.commit()
                results.append({
                    'hop': ttl,
                    'ip': reply.src,
                    'rtt': round(rtt, 2)
                })
            except Exception as e:
                db.session.rollback()
                return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})

            # 如果收到ICMP端口不可达消息，说明到达目标
            if reply.haslayer(ICMP) and reply[ICMP].type == 3 and reply[ICMP].code == 3:
                break

        return jsonify({
            'status': 'success',
            'message': 'UDP跟踪完成',
            'results': results,
            'target_ip': target_ip,
            'trace_time': trace_time.strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/tcp_traceroute/<int:ip_int>/<int:fyip_id>', methods=['POST'])
def do_tcp_traceroute(ip_int, fyip_id):
    """执行TCP traceroute并保存结果"""
    if not SCAPY_AVAILABLE:
        return jsonify({'status': 'error', 'message': 'Scapy库不可用，无法执行TCP traceroute功能'})

    try:
        # 删除同一目标IP和fyip_id的TCP跟踪记录
        db.session.execute(
            """DELETE FROM traceroute_records 
               WHERE target_ip = :target_ip 
               AND fyip_id = :fyip_id
               AND trace_type = 'TCP'""",  # 只删除TCP类型的记录
            {'target_ip': ip_int, 'fyip_id': fyip_id}
        )
        db.session.commit()

        # 将整数IP转换为点分十进制格式
        target_ip = int_to_ip(ip_int)
        max_hops = 10  # 最大跳数限制为10跳
        results = []
        
        # 记录跟踪开始时间
        trace_time = datetime.now()

        for ttl in range(1, max_hops + 1):
            # 构造和发送TCP SYN数据包
            pkt = IP(dst=target_ip, ttl=ttl) / TCP(dport=80, flags="S")
            start_time = time.time()
            reply = sr1(pkt, timeout=2, verbose=0)
            rtt = (time.time() - start_time) * 1000  # 转换为毫秒

            if reply is None:
                # 超时的情况，记录为*
                trace_record = {
                    'target_ip': ip_int,
                    'hop_number': ttl,
                    'hop_ip': 0,  # 用0表示超时
                    'rtt_ms': 0.0,  # 超时时RTT记为0
                    'trace_time': trace_time,  # 每一跳都记录相同的时间
                    'fyip_id': fyip_id,  # 添加fyip_id
                    'trace_type': 'TCP'  # 添加跟踪类型
                }
                
                # 插入记录
                try:
                    db.session.execute(
                        """INSERT INTO traceroute_records 
                           (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                           VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                        trace_record
                    )
                    db.session.commit()
                    results.append({
                        'hop': ttl,
                        'ip': '*',  # 显示为星号
                        'rtt': 0
                    })
                except Exception as e:
                    db.session.rollback()
                    return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})
                
                continue

            # 有响应的情况
            hop_ip = ip_to_int(reply.src)
            
            # 保存到数据库
            trace_record = {
                'target_ip': ip_int,
                'hop_number': ttl,
                'hop_ip': hop_ip,
                'rtt_ms': rtt,
                'trace_time': trace_time,
                'fyip_id': fyip_id,
                'trace_type': 'TCP'  # 添加跟踪类型
            }
            
            try:
                db.session.execute(
                    """INSERT INTO traceroute_records 
                       (target_ip, hop_number, hop_ip, rtt_ms, trace_time, fyip_id, trace_type) 
                       VALUES (:target_ip, :hop_number, :hop_ip, :rtt_ms, :trace_time, :fyip_id, :trace_type)""",
                    trace_record
                )
                db.session.commit()
                results.append({
                    'hop': ttl,
                    'ip': reply.src,
                    'rtt': round(rtt, 2)
                })
            except Exception as e:
                db.session.rollback()
                return jsonify({'status': 'error', 'message': f'数据库错误: {str(e)}'})

            # 如果收到TCP RST或者到达目标IP，结束跟踪
            if reply.haslayer(TCP) and (reply[TCP].flags & 0x04) or reply.src == target_ip:
                break

        return jsonify({
            'status': 'success',
            'message': 'TCP跟踪完成',
            'results': results,
            'target_ip': target_ip,
            'trace_time': trace_time.strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@ip_bp.route('/route_analysis')
def route_analysis():
    """路由分析页面"""
    return render_template('route_analysis.html') 

def ip_range_to_network(ip_range):
    """将IP范围转换为网络地址和掩码"""
    try:
        start_ip, end_ip = ip_range.split('-')
        start_ip = start_ip.strip()
        end_ip = end_ip.strip()
        
        # 计算这个范围是否可以表示为一个网段
        start_int = int(ipaddress.IPv4Address(start_ip))
        end_int = int(ipaddress.IPv4Address(end_ip))
        
        # 计算掩码位数
        mask_bits = 32
        while mask_bits > 0:
            network = ipaddress.IPv4Network(f"{start_ip}/{mask_bits}", strict=False)
            if int(network.network_address) == start_int and int(network.broadcast_address) == end_int:
                return network
            mask_bits -= 1
        return None
    except Exception as e:
        print(f"Error converting IP range to network: {e}")
        return None

def is_ip_range_within_network(ip_range, network_str):
    """检查IP范围是否在指定网段内"""
    try:
        # 解析搜索的网段
        search_network = ipaddress.IPv4Network(network_str, strict=False)
        
        # 解析IP范围
        start_ip, end_ip = ip_range.split('-')
        start_ip = start_ip.strip()
        end_ip = end_ip.strip()
        
        # 将起始IP和结束IP转换为整数进行比较
        start_int = int(ipaddress.IPv4Address(start_ip))
        end_int = int(ipaddress.IPv4Address(end_ip))
        
        # 获取搜索网段的起始和结束IP
        network_start = int(search_network.network_address)
        network_end = int(search_network.broadcast_address)
        
        # IP范围必须完全在搜索网段内
        return start_int >= network_start and end_int <= network_end
    except Exception as e:
        print(f"Error checking IP range within network: {e}")
        return False

@ip_bp.route('/import_template')
def download_template():
    """下载CSV导入模板"""
    return send_file(
        os.path.join(current_app.static_folder, 'templates', 'ip_import_template.csv'),
        as_attachment=True,
        download_name='ip_import_template.csv'
    )

@ip_bp.route('/import_csv', methods=['POST'])
def import_csv():
    """导入CSV文件数据"""
    if 'file' not in request.files:
        return jsonify({'status': 'error', 'message': '没有上传文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'status': 'error', 'message': '没有选择文件'})
    
    if not file.filename.endswith('.csv'):
        return jsonify({'status': 'error', 'message': '请上传CSV文件'})
    
    try:
        # 读取文件内容
        file_content = file.read()
        
        # 使用chardet检测文件编码
        result = chardet.detect(file_content)
        encoding = result['encoding']
        
        # 如果检测失败，尝试常用编码
        if not encoding:
            encodings = ['utf-8', 'gbk', 'gb2312', 'ansi']
            for enc in encodings:
                try:
                    file_content.decode(enc)
                    encoding = enc
                    break
                except:
                    continue
        
        if not encoding:
            return jsonify({
                'status': 'error',
                'message': '无法识别文件编码，请确保文件使用UTF-8、GBK或ANSI编码'
            })
        
        # 解码文件内容
        try:
            decoded_content = file_content.decode(encoding)
        except UnicodeDecodeError as e:
            return jsonify({
                'status': 'error',
                'message': f'文件编码错误：{str(e)}，检测到的编码为{encoding}，请尝试使用UTF-8编码保存文件'
            })
        
        # 创建CSV读取器
        stream = io.StringIO(decoded_content)
        csv_reader = csv.DictReader(stream)
        
        success_count = 0
        error_records = []
        
        for row_num, row in enumerate(csv_reader, start=2):  # 从2开始计数，因为第1行是标题
            try:
                # IP地址转换
                ip_begin = ip_to_int(row['开始IP'])
                ip_end = ip_to_int(row['结束IP'])
                
                # MAC地址处理
                mac_str = row['MAC地址'].strip()
                if mac_str:
                    mac_bytes = bytes.fromhex(mac_str.replace(':', '').replace('-', ''))
                else:
                    mac_bytes = None
                
                # 创建记录
                record = FYIP(
                    ipBegin=ip_begin,
                    ipEnd=ip_end,
                    name=row['资产名称'],
                    group=row['资产组'],
                    user=row['使用人'],
                    type=row['资产类型'],
                    system=row['操作系统'],
                    mac=mac_bytes,
                    manufacturer=row['制造商'],
                    port=row['端口'],
                    remark=row['备注'],
                    country=row['国家'],
                    province=row['省份'],
                    city=row['城市'],
                    county=row['区县'],
                    address=row['详细地址'],
                    isp=row['ISP'],
                    longitude=float(row['经度']) if row['经度'] else None,
                    latitude=float(row['纬度']) if row['纬度'] else None,
                    isused=int(row['是否使用']) if row['是否使用'] else 1
                )
                
                db.session.add(record)
                success_count += 1
                
            except Exception as e:
                error_records.append({
                    'row': row_num,
                    'error': str(e)
                })
        
        # 提交事务
        db.session.commit()
        
        # 返回结果
        result = {
            'status': 'success',
            'message': f'成功导入 {success_count} 条记录 (文件编码: {encoding})'
        }
        
        if error_records:
            result['errors'] = error_records
            result['message'] += f'，{len(error_records)} 条记录导入失败'
        
        return jsonify(result)
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'导入失败：{str(e)}'
        })

@ip_bp.route('/find_same_hops', methods=['GET'])
def find_same_hops():
    """查找具有相同跳跃路径的记录，不区分跟踪类型"""
    try:
        # 使用SQLite兼容的语法
        latest_traces = """
        WITH OrderedHops AS (
            -- 先对hop_ip进行排序
            SELECT 
                fyip_id,
                trace_time,
                hop_number,
                CASE 
                    WHEN hop_ip = 0 THEN '*' 
                    ELSE CAST(hop_ip AS TEXT) 
                END as hop_value
            FROM traceroute_records
            ORDER BY fyip_id, trace_time, hop_number
        ),
        PathSequences AS (
            -- 然后进行分组拼接，不包含trace_type
            SELECT 
                fyip_id,
                trace_time,
                GROUP_CONCAT(hop_value) as path
            FROM OrderedHops
            GROUP BY fyip_id, trace_time
        ),
        -- 找出最新的路径记录
        LatestPaths AS (
            SELECT 
                fyip_id,
                path,
                MAX(trace_time) as latest_time
            FROM PathSequences
            GROUP BY fyip_id
        ),
        -- 找出具有相同路径的记录
        SimilarPaths AS (
            SELECT 
                path,
                GROUP_CONCAT(fyip_id) as fyip_ids,
                COUNT(*) as path_count
            FROM LatestPaths
            GROUP BY path
            HAVING COUNT(*) > 1
        )
        -- 获取相关的IP范围信息
        SELECT 
            sp.path,
            sp.fyip_ids,
            sp.path_count,
            GROUP_CONCAT(f.name) as names,
            GROUP_CONCAT(f.address) as addresses
        FROM SimilarPaths sp
        INNER JOIN fyip f ON f.id IN (
            SELECT CAST(value AS INTEGER)
            FROM json_each('["' || REPLACE(sp.fyip_ids, ',', '","') || '"]')
        )
        GROUP BY sp.path, sp.fyip_ids, sp.path_count
        """
        
        results = db.session.execute(latest_traces).fetchall()
        
        if not results:
            return jsonify({
                'status': 'success',
                'data': []
            })

        similar_routes = []
        for row in results:
            # 将路径字符串转换为IP地址列表
            hop_ips = []
            for hop_ip in row.path.split(','):
                if hop_ip == '*':
                    hop_ips.append('*')
                else:
                    try:
                        hop_ips.append(int_to_ip(int(hop_ip)))
                    except:
                        hop_ips.append('无效IP')
            
            # 获取相关的IP范围记录
            fyip_ids = [int(id) for id in row.fyip_ids.split(',')]
            ip_records = FYIP.query.filter(FYIP.id.in_(fyip_ids)).all()
            
            # 将FYIP对象转换为字典
            ip_records_dict = []
            for record in ip_records:
                ip_records_dict.append({
                    'id': record.id,
                    'ipBegin': record.ipBegin,
                    'ipEnd': record.ipEnd,
                    'name': record.name or '',
                    'address': record.address or '',
                    'group': record.group or '',
                    'isp': record.isp or ''
                })
            
            similar_routes.append({
                'hop_sequence': hop_ips,
                'ip_records': ip_records_dict,
                'record_count': row.path_count,
                'names': row.names.split(',') if row.names else [],
                'addresses': row.addresses.split(',') if row.addresses else []
            })
        
        return jsonify({
            'status': 'success',
            'data': similar_routes
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@ip_bp.route('/port_scan', methods=['POST'])
def port_scan():
    """处理端口扫描请求"""
    try:
        data = request.get_json()
        if not data or 'input' not in data:
            return jsonify({
                'status': 'error',
                'message': '无效的请求数据'
            })
            
        input_text = data['input']
        if not input_text.strip():
            return jsonify({
                'status': 'error',
                'message': '请输入要扫描的IP和端口'
            })
            
        # 创建扫描器实例
        scanner = PortScanner(timeout=1.0, max_workers=50)
        
        # 执行扫描
        results = scanner.scan(input_text)
        
        return jsonify({
            'status': 'success',
            'results': results
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'扫描失败：{str(e)}'
        })

@ip_bp.route('/port_scan_page')
def port_scan_page():
    """端口扫描页面"""
    return render_template('port_scan.html')

@ip_bp.route('/ip_sort_page')
def ip_sort_page():
    """IP地址整理排序页面"""
    return render_template('ip_sort.html')

@ip_bp.route('/ip_sort', methods=['POST'])
def ip_sort():
    """处理IP地址整理排序请求"""
    try:
        data = request.get_json()
        if not data or 'input' not in data:
            return jsonify({
                'status': 'error',
                'message': '无效的输入数据'
            })
        
        # 获取输入文本
        input_text = data['input']
        
        # 按行分割
        lines = input_text.strip().split('\n')
        
        # 处理每一行
        ip_list = []
        for line in lines:
            # 去除引号和空格
            line = line.strip().strip("'").strip('"')
            
            # 使用多种分隔符分割
            # 1. 先按分号分割
            parts = line.split(';')
            # 2. 对每个部分按逗号分割
            parts = [p.split(',') for p in parts]
            # 3. 展平列表
            parts = [item for sublist in parts for item in sublist]
            # 4. 对每个部分按空格分割
            parts = [p.split() for p in parts]
            # 5. 展平列表
            parts = [item for sublist in parts for item in sublist]
            
            # 处理每个可能的IP地址
            for part in parts:
                # 去除引号和空格
                part = part.strip().strip("'").strip('"')
                # 去除所有空格
                part = ''.join(part.split())
                
                # 验证IP地址格式
                try:
                    # 尝试解析IP地址
                    ipaddress.ip_address(part)
                    ip_list.append(part)
                except ValueError:
                    continue
        
        # 去重并排序
        ip_list = sorted(set(ip_list), key=lambda ip: [int(x) for x in ip.split('.')])
        
        return jsonify({
            'status': 'success',
            'results': ip_list
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@ip_bp.route('/dns_resolve_page')
def dns_resolve_page():
    """DNS解析页面"""
    return render_template('dns_resolve.html')

@ip_bp.route('/dns_resolve', methods=['POST'])
def dns_resolve():
    """处理DNS解析请求"""
    try:
        data = request.get_json()
        if not data or 'dns_servers' not in data or 'domains' not in data:
            return jsonify({
                'status': 'error',
                'message': '无效的请求数据'
            })
        
        dns_servers = data['dns_servers']
        domains = data['domains']
        
        results = []
        resolver = dns.resolver.Resolver()
        resolver.timeout = 5
        
        for domain in domains:
            domain_result = {
                'domain': domain,
                'results': {}
            }
            
            for dns_server in dns_servers:
                try:
                    resolver.nameservers = [dns_server]
                    answers = resolver.resolve(domain, 'A')
                    # 格式化IP地址显示
                    ip_addresses = [f"• {rdata.address}" for rdata in answers]
                    if len(ip_addresses) > 1:
                        domain_result['results'][dns_server] = "\n".join(ip_addresses)
                    else:
                        domain_result['results'][dns_server] = ip_addresses[0] if ip_addresses else "未找到IP地址"
                except dns.resolver.NXDOMAIN:
                    domain_result['results'][dns_server] = "❌ 错误: 域名不存在"
                except dns.resolver.Timeout:
                    domain_result['results'][dns_server] = "❌ 错误: 查询超时"
                except dns.resolver.NoNameservers:
                    domain_result['results'][dns_server] = "❌ 错误: DNS服务器无响应"
                except dns.resolver.NoAnswer:
                    domain_result['results'][dns_server] = "❌ 错误: 未收到答复"
                except Exception as e:
                    domain_result['results'][dns_server] = f"❌ 错误: {str(e)}"
            
            results.append(domain_result)
        
        return jsonify({
            'status': 'success',
            'results': results
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

# 爬虫相关路由
@ip_bp.route('/crawler_config_page')
def crawler_config_page():
    """爬虫配置页面"""
    return render_template('crawler_config.html')

@ip_bp.route('/crawler_config', methods=['GET', 'POST'])
def crawler_config():
    """获取或保存爬虫配置"""
    if request.method == 'GET':
        try:
            with open('crawler_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            return jsonify({
                'status': 'success',
                'config': config
            })
        except FileNotFoundError:
            return jsonify({
                'status': 'success',
                'config': {
                    'allowed_domains': '',
                    'start_urls': '',
                    'depth_limit': '5',
                    'download_delay': '1',
                    'concurrent_requests': '16',
                    'robotstxt_obey': True
                }
            })
    else:
        try:
            config = request.get_json()
            with open('crawler_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            # 更新爬虫设置
            update_spider_settings(config)
            return jsonify({'status': 'success'})
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': str(e)
            })

@ip_bp.route('/start_crawler', methods=['POST'])
def start_crawler():
    """启动爬虫"""
    try:
        # 检查配置文件是否存在
        if not os.path.exists('crawler_config.json'):
            return jsonify({
                'status': 'error',
                'message': '请先保存爬虫配置'
            })
        
        # 先测试导入Scrapy是否成功
        try:
            import scrapy
            from scrapy.crawler import CrawlerProcess
        except ImportError as e:
            error_msg = f"导入Scrapy失败，请确保已正确安装: {str(e)}"
            with open('crawler_status.json', 'w', encoding='utf-8') as f:
                json.dump({'status': 'error', 'message': error_msg}, f)
            with open('crawler_log.txt', 'w', encoding='utf-8') as f:
                f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] {error_msg}\n')
                f.write('请运行 update_deps.bat 更新依赖包，或手动执行: pip install "scrapy>=2.11.0" "twisted>=22.10.0"')
            return jsonify({'status': 'error', 'message': error_msg})
        
        # 启动前写入状态文件
        with open('crawler_status.json', 'w', encoding='utf-8') as f:
            json.dump({'status': 'running'}, f)
        # 启动前清空日志文件
        with open('crawler_log.txt', 'w', encoding='utf-8') as f:
            f.write('')
            
        # 记录启动信息到日志
        with open('crawler_log.txt', 'a', encoding='utf-8') as f:
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 正在启动爬虫...\n')
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 当前工作目录: {os.getcwd()}\n')
            
            # 读取并记录配置信息
            try:
                with open('crawler_config.json', 'r', encoding='utf-8') as cf:
                    config = json.load(cf)
                    f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 爬虫配置: {json.dumps(config, ensure_ascii=False)}\n')
            except Exception as e:
                f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 读取配置失败: {str(e)}\n')
            
        # 启动爬虫进程并捕获输出 - 使用Python模块方式启动scrapy
        import sys
        python_exe = sys.executable  # 获取当前Python解释器路径
        
        # 记录Python路径
        with open('crawler_log.txt', 'a', encoding='utf-8') as f:
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 使用Python解释器: {python_exe}\n')
        
        # 使用当前Python解释器运行scrapy
        process = subprocess.Popen(
            [python_exe, '-m', 'scrapy', 'crawl', 'link_spider'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()  # 确保在正确的目录中运行
        )
        
        # 启动一个线程来读取进程输出并写入日志
        def read_output():
            with open('crawler_log.txt', 'a', encoding='utf-8') as f:
                f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 爬虫进程启动，开始读取输出...\n')
            
            for line in process.stdout:
                with open('crawler_log.txt', 'a', encoding='utf-8') as f:
                    f.write(f'[STDOUT] {line}')
            
            for line in process.stderr:
                with open('crawler_log.txt', 'a', encoding='utf-8') as f:
                    f.write(f'[STDERR] {line}')
            
            # 检查进程退出码
            return_code = process.wait()
            with open('crawler_log.txt', 'a', encoding='utf-8') as f:
                f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 爬虫进程退出，返回码: {return_code}\n')
                
            # 根据进程退出码更新状态
            if return_code == 0:
                # 进程正常退出，更新状态为completed
                with open('crawler_status.json', 'w', encoding='utf-8') as f:
                    json.dump({'status': 'completed', 'message': '爬虫任务已完成'}, f)
            else:
                # 进程异常退出，更新状态为error
                with open('crawler_status.json', 'w', encoding='utf-8') as f:
                    json.dump({'status': 'error', 'message': f'爬虫进程异常退出，返回码: {return_code}'}, f)
                    
        threading.Thread(target=read_output, daemon=True).start()
        
        return jsonify({'status': 'success'})
    except Exception as e:
        # 记录错误信息到日志
        with open('crawler_log.txt', 'a', encoding='utf-8') as f:
            f.write(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 启动爬虫失败: {str(e)}\n')
            
        with open('crawler_status.json', 'w', encoding='utf-8') as f:
            json.dump({'status': 'error', 'message': f'启动爬虫失败: {str(e)}'}, f)
            
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@ip_bp.route('/crawler_progress')
def crawler_progress():
    """获取爬虫进度"""
    try:
        # 读取爬虫状态
        try:
            with open('crawler_status.json', 'r', encoding='utf-8') as f:
                status_info = json.load(f)
            status = status_info.get('status', 'running')
            message = status_info.get('message', '')
        except FileNotFoundError:
            status = 'running'
            message = ''
        # 从数据库获取最新结果
        conn = sqlite3.connect('links.db')
        cursor = conn.cursor()
        # 获取总记录数
        cursor.execute('SELECT COUNT(*) FROM links')
        total = cursor.fetchone()[0]
        # 获取最新记录
        cursor.execute('''
            SELECT url, title, depth, status, crawl_time
            FROM links
            ORDER BY crawl_time DESC
            LIMIT 100
        ''')
        results = []
        for row in cursor.fetchall():
            results.append({
                'url': row[0],
                'title': row[1],
                'depth': row[2],
                'status': row[3],
                'crawl_time': row[4]
            })
        conn.close()
        return jsonify({
            'status': 'success',
            'progress': {
                'current': total,
                'total': total,
                'status': status,
                'message': message
            },
            'results': results
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@ip_bp.route('/export_crawler_data')
def export_crawler_data():
    """导出爬虫数据"""
    try:
        # 创建临时CSV文件（使用绝对路径）
        temp_file = os.path.join(os.getcwd(), 'crawler_results.csv')
        conn = sqlite3.connect('links.db')
        cursor = conn.cursor()
        
        # 获取所有数据
        cursor.execute('SELECT * FROM links')
        rows = cursor.fetchall()
        
        # 获取列名
        cursor.execute('PRAGMA table_info(links)')
        columns = [col[1] for col in cursor.fetchall()]
        
        # 写入CSV文件
        with open(temp_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(columns)
            writer.writerows(rows)
        
        conn.close()
        
        # 发送文件（使用绝对路径）
        return send_file(
            temp_file,
            mimetype='text/csv',
            as_attachment=True,
            download_name='crawler_results.csv'
        )
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@ip_bp.route('/clear_crawler_database', methods=['POST'])
def clear_crawler_database():
    """清空爬虫数据库"""
    try:
        conn = sqlite3.connect('links.db')
        cursor = conn.cursor()
        cursor.execute('DELETE FROM links')
        conn.commit()
        conn.close()
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

def update_spider_settings(config):
    """更新爬虫设置"""
    settings_path = os.path.join('web_crawler', 'settings.py')
    try:
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新设置
        settings = {
            'DEPTH_LIMIT': config['depth_limit'],
            'DOWNLOAD_DELAY': config['download_delay'],
            'CONCURRENT_REQUESTS': config['concurrent_requests'],
            'ROBOTSTXT_OBEY': 'True' if config['robotstxt_obey'] else 'False'
        }
        
        for key, value in settings.items():
            pattern = f"{key} = .*"
            replacement = f"{key} = {value}"
            content = re.sub(pattern, replacement, content)
        
        with open(settings_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
    except Exception as e:
        raise Exception(f"更新爬虫设置时出错：{str(e)}")

@ip_bp.route('/crawler_log')
def crawler_log():
    try:
        if not os.path.exists('crawler_log.txt'):
            return jsonify({'status': 'success', 'log': []})
        with open('crawler_log.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        # 只返回最新的50条
        log = [line.strip() for line in lines[-50:]]
        return jsonify({'status': 'success', 'log': log})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

# 添加子进程函数：处理指定IP范围
def export_ip_range(a_blocks, process_id, result_queue):
    try:
        # 为每个进程创建临时数据库
        temp_db_path = f'qqwry_temp_{process_id}.db'
        conn = sqlite3.connect(temp_db_path)
        cursor = conn.cursor()
        
        # 创建临时表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS ip_locations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip_start INTEGER NOT NULL,
            ip_end INTEGER NOT NULL,
            ip_start_str TEXT NOT NULL,
            ip_end_str TEXT NOT NULL,
            country TEXT,
            area TEXT,
            create_time TEXT
        )
        ''')
        
        # 清空现有数据
        cursor.execute('DELETE FROM ip_locations')
        conn.commit()
        
        # 初始化QQwry实例
        q = QQwry()
        is_loaded = q.load_file('qqwry.dat')
        if not is_loaded:
            raise Exception(f"进程 {process_id}: QQ纯真数据库加载失败")
        
        # 获取当前时间
        create_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 处理进度日志
        with open(f'export_range_{process_id}.log', 'w', encoding='utf-8') as f:
            f.write(f'[{datetime.now()}] 进程 {process_id} 开始导出A类段: {a_blocks}\n')
        
        # 记录数据
        records = []
        records_found = 0
        
        # 对每个A类网段单独处理
        for a in a_blocks:
            # 记录当前网段处理信息
            with open(f'export_range_{process_id}.log', 'a', encoding='utf-8') as f:
                f.write(f'[{datetime.now()}] 进程 {process_id} 开始处理A类网段: {a}.0.0.0/8\n')
            
            # 计算当前A类网段的开始和结束IP
            start_ip = struct.unpack("!I", socket.inet_aton(f"{a}.0.0.0"))[0]
            end_ip = struct.unpack("!I", socket.inet_aton(f"{a}.255.255.255"))[0]
            
            # 当前段的信息
            last_ip = None
            current_segment_start = None
            current_info = None
            
            # 进度报告变量
            total_ips = end_ip - start_ip + 1
            processed = 0
            last_report_time = time.time()
            report_interval = 10  # 每10秒报告一次进度
            
            # 逐个IP扫描
            for ip_int in range(start_ip, end_ip + 1):
                # 每10秒报告一次进度
                processed += 1
                current_time = time.time()
                if current_time - last_report_time >= report_interval:
                    percentage = (processed / total_ips) * 100
                    result_queue.put((process_id, 'progress', f"{a}.x.x.x - {percentage:.1f}%", records_found))
                    last_report_time = current_time
                
                # 转换为点分十进制格式
                ip_str = socket.inet_ntoa(struct.pack('!I', ip_int))
                
                try:
                    # 查询IP信息
                    result = q.lookup(ip_str)
                    if result:
                        country, area = result
                        current_location = (country, area)
                        
                        # 如果这是第一个IP或者信息与上一个不同
                        if last_ip is None or current_info != current_location:
                            # 如果已有一个段，保存它
                            if current_segment_start is not None and current_info is not None:
                                records.append((
                                    current_segment_start,
                                    last_ip,
                                    socket.inet_ntoa(struct.pack('!I', current_segment_start)),
                                    socket.inet_ntoa(struct.pack('!I', last_ip)),
                                    current_info[0],
                                    current_info[1],
                                    create_time
                                ))
                                records_found += 1
                                
                                # 每积累1000条记录写入一次
                                if len(records) >= 1000:
                                    cursor.executemany(
                                        'INSERT INTO ip_locations (ip_start, ip_end, ip_start_str, ip_end_str, country, area, create_time) VALUES (?, ?, ?, ?, ?, ?, ?)',
                                        records
                                    )
                                    conn.commit()
                                    records = []
                            
                            # 开始新的段
                            current_segment_start = ip_int
                            current_info = current_location
                    
                    # 更新最后处理的IP
                    last_ip = ip_int
                
                except Exception as e:
                    # 记录错误但继续
                    with open(f'export_range_errors_{process_id}.log', 'a', encoding='utf-8') as f:
                        f.write(f"处理IP {ip_str}时错误: {str(e)}\n")
            
            # 保存最后一个段
            if current_segment_start is not None and current_info is not None:
                records.append((
                    current_segment_start,
                    last_ip,
                    socket.inet_ntoa(struct.pack('!I', current_segment_start)),
                    socket.inet_ntoa(struct.pack('!I', last_ip)),
                    current_info[0],
                    current_info[1],
                    create_time
                ))
                records_found += 1
                
            # 报告完成A类网段
            with open(f'export_range_{process_id}.log', 'a', encoding='utf-8') as f:
                f.write(f'[{datetime.now()}] 进程 {process_id} 完成处理A类网段: {a}.0.0.0/8，记录数: {records_found}\n')
            
            # 提交每个A类网段的剩余记录
            if records:
                cursor.executemany(
                    'INSERT INTO ip_locations (ip_start, ip_end, ip_start_str, ip_end_str, country, area, create_time) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    records
                )
                conn.commit()
                records = []
        
        # 提交剩余记录
        if records:
            cursor.executemany(
                'INSERT INTO ip_locations (ip_start, ip_end, ip_start_str, ip_end_str, country, area, create_time) VALUES (?, ?, ?, ?, ?, ?, ?)',
                records
            )
            conn.commit()
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ip_start ON ip_locations(ip_start)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_ip_end ON ip_locations(ip_end)')
        conn.commit()
        
        # 关闭连接
        conn.close()
        
        # 记录完成
        with open(f'export_range_{process_id}.log', 'a', encoding='utf-8') as f:
            f.write(f'[{datetime.now()}] 进程 {process_id} 导出完成，总记录数: {records_found}\n')
        
        # 报告完成
        result_queue.put((process_id, 'completed', None, records_found))
        
    except Exception as e:
        # 记录错误
        with open(f'export_range_errors_{process_id}.log', 'a', encoding='utf-8') as f:
            f.write(f"进程 {process_id} 错误: {str(e)}\n")
        
        # 报告错误
        result_queue.put((process_id, 'error', None, 0))

# 合并所有临时数据库到主数据库
def merge_databases(num_processes):
    try:
        # 打开主数据库
        main_conn = sqlite3.connect('qqwry.db')
        main_cursor = main_conn.cursor()
        
        # 获取当前时间
        merge_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 合并所有临时数据库
        for i in range(num_processes):
            temp_db_path = f'qqwry_temp_{i}.db'
            if os.path.exists(temp_db_path):
                # 打开临时数据库
                temp_conn = sqlite3.connect(temp_db_path)
                temp_conn.row_factory = sqlite3.Row
                temp_cursor = temp_conn.cursor()
                
                # 查询所有记录
                temp_cursor.execute('SELECT * FROM ip_locations')
                batch = []
                batch_size = 5000
                
                # 批量插入
                while True:
                    rows = temp_cursor.fetchmany(batch_size)
                    if not rows:
                        break
                        
                    batch = []
                    for row in rows:
                        batch.append((
                            row['ip_start'],
                            row['ip_end'],
                            row['ip_start_str'],
                            row['ip_end_str'],
                            row['country'],
                            row['area'],
                            merge_time
                        ))
                    
                    # 插入主数据库
                    main_cursor.executemany(
                        'INSERT INTO ip_locations (ip_start, ip_end, ip_start_str, ip_end_str, country, area, create_time) VALUES (?, ?, ?, ?, ?, ?, ?)',
                        batch
                    )
                    main_conn.commit()
                
                # 关闭临时连接
                temp_conn.close()
                
                # 删除临时数据库文件
                try:
                    os.remove(temp_db_path)
                except:
                    pass
        
        # 创建索引
        main_cursor.execute('CREATE INDEX IF NOT EXISTS idx_ip_start ON ip_locations(ip_start)')
        main_cursor.execute('CREATE INDEX IF NOT EXISTS idx_ip_end ON ip_locations(ip_end)')
        main_conn.commit()
        
        # 关闭连接
        main_conn.close()
        
    except Exception as e:
        # 记录错误
        with open('qqwry_export_errors.log', 'a', encoding='utf-8') as f:
            f.write(f"合并数据库错误: {str(e)}\n")

# 已删除 export_qqwry_to_sqlite 路由

# 已删除 export_qqwry_progress 路由

# 已删除 qqwry_db_info 路由和相关函数

# 已删除 export_specific_range 和 import_qqwry_record 路由











