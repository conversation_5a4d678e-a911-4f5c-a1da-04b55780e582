# SPDX-License-Identifier: BSD-3-Clause
# This file is part of Scapy
# See https://scapy.net/ for more information

"""
/*
 * Copyright (c) 1982, 1986, 1993
 *	The Regents of the University of California.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the University nor the names of its contributors
 *    may be used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 *	@(#)if_ether.h	8.1 (Berkeley) 6/10/93
 */
"""

# To quote Python's get-pip:

# Hi There!
#
# You may be wondering what this giant blob of binary data here is, you might
# even be worried that we're up to something nefarious (good for you for being
# paranoid!). This is a base85 encoding of a zip file, this zip file contains
# a version of '/etc/ethertypes', generated from OpenBSD's own copy, so that
# we are able to use it when not available on your OS.

# This file is automatically generated using
# scapy/tools/generate_ethertypes.py

import gzip
from base64 import b85decode

def _d(x: str) -> str:
    return gzip.decompress(
        b85decode(x.replace("\n", ""))
    ).decode()


DATA = _d("""
ABzY8N|hyM0{^91ZExbZ7XI#Eaioz}AWb2>6zJa3jGPeKXcNeglyY@-KbXWoE+IxqXv@Ff<IvJNyVvd
RMru`2KTnQ*-kxK=kS}1DTb^gUgmupL9Lm#y7x?k{3AafB>m=n6^CHTV6)&I=xJ;}8aq!6UL>!9?$pv
`GMJXbYp80SsD}m)4js=fFWN&Z9pC^&;iWd2T;Oc#8Qj`#hV;aMX!&)3O3HkNH4X`cC!>{f3)6-KcVH
s<QeA8w{k!-R(&&s0BU)Zm*<9@~S;x9lG&iU2I=)OS_{4K+y`7Yt#w*2}0pYQOEr3ouK-&?KL`On_<c
lct7y<|chvh?8HV;Dvs{@?Qj9NV@5F|8gPShT~#^zVITjnOp>4Z)J<;u$39a{5<La1I7F3`s`A&yz_S
8pk;=3J7zS6)7tdbliopL#BZWG6s6{rU60}8ziKohy1A#jh?r{kVsEWGInRT9ffhAGeRzh#*Z_utKvG
J!8!h;;zVPl-L&)O46+<vLk+6kd<)k*S$R&ZsTtJ4T@6?+zhG_4qcv<#gRPcdz}6j|1u9q<#np0&TXz
4j+iUG^alOkQc?vy3=YAbK&|qIf1b>P2SVhR?&?j@145>yMs!3G@=R9R6kif=#Vs(Z_r%4vh)K<>Hq+
<<{$+8p6phA&wP968%zdMFDXfV{V<mRtsU~DPggHE@n^MG7_1>P|&lZX{1Sy0z`Z)r!Lrsw6wsVMpW?
HK2ltJ-jLqx0sNmFysrtOQHs2a&&|tz=2rn|GRIdZ)S?i&94$))<;o{#?SHIG~#@{`Oxho^)8Z*Xts+
>08!2bkEWTZVwAL^809Umhnh-p#34`C5KFu7+M?bN<8PW<Q?Ctyu;7%$}`zuctImgcD$->&e(6(>3vI
0^nSOmOLV#1WJMBznTlw4ISAr-uKC_)eM`&ZWNVS{&wla*c6)G>vc$%3CL3_+lz20L{GM;1_te<703i
?`_lI^WSS$(VmP*k51VPUC0-X?v44uu1t2PkH(*J-3Atb2f5W<EZ!WPz7Kp>%>Y0p*g=mT&CA#?gLQG
nOiHyYraJt+m~t@zxV%GtwEUqJ4&4M%5Y*%gLH0kL?>Di_?FQ|Df#>3p6Bv4y1YER~}7d5RxDen3MKl
%l=PF){8<F!LndaJfe_2vz3m&oiG{AUNPBNw&cn$~l<@>TwVCUY`Z+8}O1S7f+~F(R&tk6?D(gdM{$>
Rn<4K#*sU<g`u+3ZU>iR>aI8mom)CpaNUWnS0o#jeZ};RS_A`+dJ44vVVpi<t6{4oP0AB^D2VY;QF7-
n#(IQ$|5&1452|KRNC4Z%1vK#EY77t=2Nd>_s9>i;mNIOV_R?23er;;32udbPPYetAO)8EQ`17Gf7XE
xTR#~jS#DYC0ZV@}Ed*NEwwe3d~neYn)M>#>D8)Mv#ZOs&hfi8v?oJXQkPgv{a;n8C$T7-sS&5nVt64
3CMka!ezgMt}S4aQ?-&d7Ld*IqO<wT(I}c2#QvJLS7F7g@3+o;`O$l|*;=9?z5VMKOw)VH3sxy3dkFI
VNI0ZDow2cBUb9e^Y30bhgn0_TP}DJK|GIDZnGc(&<5;MHt0_SeI2E2UA)*lCdrm9n%8|33&dPMA!($
BeGp4+_@b(ONDKsYh!a>CeMV{6fJ^!pV>J`AX&IdMSxL9KXbeelAWJWK~Z;XWKC==mrL15*J%=!MU$A
biCg2<ZRLTd={n`S{1;TnhM99#Zecb-t+3`zIPA&-{(~AmKf(1?$G}lBDq8;~cl6&%9*#QNLVCwec<N
OhmKqF8P+pvEf7)U&VIVDTU23X?x<0nJ1(&U)3KeMCOEtquZmEVvr>9HoDTYBRLpzO|C_&2yd7U9S8d
x8u6XzCe5C^HBn#8;J{Mv?fHQZ~T0kKTOV#{)L7MZw?8Zw=}F6I|`@;_cB9iCQFa!km^)NMjV)0p5O0
It9Wbs6j~N)eT^HLjgRUss%_=PMf&%F;P9u*ST~6hdA9j;chuibd1ImYp4qN$S<NtF8BPxssUBkQWnD
F&U$qCRv7lZBIj8;lIoec7~~f;fHt<+&bZd%3<qlx^>!Ng6a)JBa`D>F0hdWe=uaEi`5|7^7xoy<ESn
?*0PX={#UqjeBZeWLU{q9#KCAV9)Iov0;<qJeLo=cam}b<Pv>zQiLf)I5b|i7iBxP(mEZtL^N^HVfqK
C4iRV~;jg|flR!@$t_-A~S5(GomD)aR0p%!kR2I@NomVW!P0cT<_uPI-J%$u+d+}VRdhdoI{H!^yy9*
dz!#na_nk<X=;-}YF(<9U<#e(i8ahOW)Y<qfft(+$o!tCRd-W@8cYU3I6UzOFu1%Q63}nV&A58unUyf
%2RbGM)EFiI+9304%UFfb)cx46Ei!h!Wkw4sF>$k`1Y>R9-RYf3VA$lCJ?Ts%S*%w&BF4{>)YAMz+=w
*xr_asB;yN6Dpn6q|b<UwHk}oTKi9D`m;tb`p5<hhN{OFC5v{lZo96UTceSs3s5)7cG#Uxypr6JsG8?
xi1$siwk3P`kwrfx{4t4<(L|e8QlwYYk+g0a+^K{&{mCav{ficYJI*Ap%%4%uFtE%{gW&zhaigCotIL
qaMC5x^*QeK^O^v+f*XnNMHCN9=Np+<jT7Lay-GBZvWNl-a_>=dIZJd|iQBr*L^VHN1f55%`jsx>}L6
<P-b`7X_NHCi!!f`?;IvE<E5`PAoA2^CPK^9J^45O2YRs)*m6e`G-%d(f9M+|!lJh2@=9txZ;RQWT(M
>}0SC;Q_b9$A{i@cIQy_4UqIdGU$?!ejC~XT@GkQ5paM
""")
